# Ignore version control files
.git
.gitignore

# Ignore node modules (will be installed in the container)
node_modules

# Ignore build output
# build  # Comment out or remove this line
output

# Ignore development and IDE files
.vscode
.idea
*.swp
*.swo

# Ignore log files
*.log

# Ignore the Docusaurus cache
.docusaurus

# Ignore environment variables file
.env

# Ignore Docker related files
Dockerfile
docker-compose.yml
.dockerignore

# Ignore any local configuration files
*.local.js

# Ignore test files
__tests__

# Ignore documentation files (if any)
/*.md
CHANGELOG.md
LICENSE

# Ignore .npmrc file
.npmrc
pnpm-lock.yaml
.pnpm-store/

# Ignore Obsidian files
.obsidian/
.trash/
.DS_Store
.MWebMetaData