# Use Node.js v20 LTS as the base image
FROM node:20-alpine

# Set the working directory in the container
WORKDIR /app

# Copy package files only
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Build directory needs to be mounted, so we ensure it exists
RUN mkdir -p build

# Expose the port the app runs on
EXPOSE 3000

# Command to run the development server
CMD ["npm", "start"]
