{"name": "nexus-docs", "version": "0.2.1", "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start --host 0.0.0.0", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "3.7.0", "@docusaurus/plugin-client-redirects": "^3.7.0", "@docusaurus/plugin-content-docs": "^3.7.0", "@docusaurus/preset-classic": "3.7.0", "@docusaurus/theme-mermaid": "3.7.0", "@mdx-js/react": "^3.0.0", "clsx": "^2.0.0", "prism-react-renderer": "^2.3.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.7.0", "@docusaurus/types": "3.7.0"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}