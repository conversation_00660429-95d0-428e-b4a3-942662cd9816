---
sidebar_position: 3
title: Terraform Best Practices
sidebar_label: terraform
---

# Terraform 

## Best Practices

- 使用 .tfvars

In Terraform, the recommended practice is to define variable values at the root level or through environment-specific variable files (`*.tfvars`). This is because modules are intended to be reusable and modular, and their configuration should be controlled externally rather than internally.

  - Reasoning:

  1. **Reusability**: Modules are designed to be reusable across different environments or projects. Hardcoding values or variable assignments within the module directory limits this reusability.
  2. **Separation of Concerns**: Keeping variable definitions and assignments outside the module directory ensures a clear separation of the module's implementation and its configuration. This makes the module easier to understand, maintain, and reuse.
  3. **Environment-Specific Configurations**: Different environments (development, staging, production) may require different configurations. Managing these configurations at the root level or through environment-specific variable files allows for better flexibility and management.
  4. **Version Control and Best Practices**: It's a best practice to keep the module code (the logic) separate from the configuration (the variables). This aligns with infrastructure-as-code principles and makes the codebase cleaner and more maintainable.

  我曾经尝试过在 `modules/lambda` 下放置 `lambda.tfvars` 不成功，不过从 modular 的角度说也是对的。

  - Assigning Values to Variables in Module Directory

  If you still want to assign values directly in the module directory for any specific reason, you can use a `.auto.tfvars` file, which Terraform automatically loads to assign variable values. However, this approach is not typically recommended for the reasons mentioned above.