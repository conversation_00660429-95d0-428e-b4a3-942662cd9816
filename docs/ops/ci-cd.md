---
sidebar_position: 1
title: Continuous Integration and Continuous Deployment (CI/CD)
sidebar_label: CI/CD
last_update: 2025-08-06
---

# Continuous Integration & Continuous Deployment (CI/CD)

> **Scope**
> This living document captures NeXus's current CI/CD approach with an emphasis on **cost‑effectiveness**, **edge scalability**, and **developer simplicity**. It supersedes earlier drafts (2024‑07) and reflects our 2025 research prioritizing GitHub Actions + CircleCI Cloud for serverless CI/CD and CircleCI self‑hosted runners for on‑premises deployments.

---

## 1  Why CI/CD?

| Goal                          | Outcome                                                                         |
| ----------------------------- | ------------------------------------------------------------------------------- |
| Shorten feedback loops        | Faster detection of integration issues, test failures, and security regressions |
| Increase deployment frequency | Small, incremental releases with automated rollbacks                            |
| Lower infrastructure spend    | Scale‑to‑zero runners and pay‑per‑second cloud build minutes                    |
| Uniform developer experience  | One YAML‑based workflow definition for all projects                             |

---

## 2  High‑Level Pipeline

```mermaid
flowchart LR
  SC[(Source Code)] --> CI["CI Jobs <br/>– lint<br/>– unit test<br/>– build image"]
  CI --> CD["CD Jobs <br/>– deploy to staging <br/>– e2e tests <br/>– promote to prod"]
  CD --> Mon[Monitoring / Alerting]
```

*Each workflow is triggered by Git events or a manual dispatch and runs on the cheapest runner that satisfies the required labels (e.g. `arm64`, `gpu`, `privileged`).*

---

## 3  Platform Matrix (Free & Low‑Cost Tiers)

| Platform                   | Free Hosted Minutes<sup>†</sup> | Concurrency            | Self‑Hosted Runners       | ARM 64 Hosted                   | Notes                             |
| -------------------------- | ------------------------------- | ---------------------- | ------------------------- | ------------------------------- | --------------------------------- |
| **GitHub Actions** 🏆      | 2 000 private / ∞ public        | 20 Linux               | Unlimited, feature‑parity | GA <br/>`runs-on: ubuntu‑arm64` | **Preferred serverless** + 20k+ actions |
| **CircleCI Free** 🥈       | 6 000                           | 30 Docker              | **Preferred self‑hosted** | Yes                             | **Primary choice** + Orbs ecosystem |
| **GitLab CI (gitlab.com)** | 400                             | Unlimited<sup>\*</sup> | Secondary option          | No (self‑host)                  | Bring‑your‑own runner = zero cost |
| **Bitbucket Pipelines**    | 50                              | 10                     | Yes                       | Beta                            | Best for Atlassian stack          |
| **Semaphore Free**         | 7 000                           | 40                     | Yes                       | Yes                             | Pay‑per‑second after quota        |
| **GCP Cloud Build**        | 2 500                           | 10                     | —                         | Multi‑arch via Buildx           | Tight GCP integration             |
| **Azure Pipelines**        | 1 800                           | 1 hosted / ∞ self      | Yes                       | Self‑host                       | Good for hybrid Windows           |
| **Vercel (Hobby)**         | ∞ builds (≤ 45 min)             | 1                      | —                         | —                               | Edge preview URLs                 |

<small><sup>†</sup> Hosted minute multipliers: Linux 1×, Windows 2×, macOS 10×. <sup>\*</sup>Bound only by runner count.</small>

---

## 4  Recommended Tooling

### 4.1  Serverless CI/CD (🏆 Preferred for Cloud)

**GitHub Actions + CircleCI Cloud** combination provides the best serverless CI/CD experience:

* **GitHub Actions**: Primary choice for public repos (unlimited minutes) and private repos with generous free tier (2,000 minutes/month)
* **CircleCI Cloud**: Excellent for burst capacity with 6,000 free minutes/month and superior Orbs ecosystem
* **Why this combo**: Complementary strengths, massive action/orb marketplaces, and cost-effective scaling

### 4.2  Self‑Hosted CI/CD (🥈 Preferred for On-Premises)

**CircleCI Self‑Hosted Runners** as the primary choice:

* **Why**: Superior runner management, excellent ARM/x86 parity, Docker/Kubernetes executors, and robust scaling
* **Deploy pattern**: CircleCI runner agents on k3s clusters or standalone VMs with auto-scaling capabilities
* **Cost**: Pay only for compute resources; unlimited build minutes with self-hosted setup

**GitLab Runners** as secondary option:

* **Why**: Still viable with unlimited minutes and free self-hosted option
* **Deploy pattern**: GitLab Runner **Helm chart** per k3s single‑node cluster (US/EU/AP). Set **HPA/KEDA** to scale manager to 0 when idle
* **Resource caps**:

  ```toml
  [[runners]]
    executor = "docker"
    [runners.docker]
      cpus   = "1"
      memory = "1g"
  ```
* **Cost**: each 512 MB VM ≈ US\$5/mo; scales to zero job pods when idle

### 4.3  ~~Drone CI~~ (Deprecated)

> **Note**: Drone CI has been deprecated from our recommended tooling in favor of CircleCI self-hosted runners for better ecosystem support and management capabilities.

### 4.4  Argo CD (GitOps‑First CD)

* **Why** Kubernetes‑native, pull‑based **delivery** controller that keeps clusters in sync with Git.
* **Deploy pattern** Helm/Kustomize install in any cluster; can manage **multi‑cluster** fleets.
* **Cost** **\$0** (Apache‑2.0). Control‑plane < 300 m CPU; idle‑scale via HPA.
* **Highlights** Declarative sync/rollback, health checks, SSO, RBAC, GUI, progressive delivery with **Argo Rollouts**.

## 5  Edge & Global Runner Topology

| Region         | Pattern         | Runner Type                    | Monthly Idle Cost | Notes                        |
| -------------- | --------------- | ------------------------------ | ----------------- | ---------------------------- |
| us‑east‑1      | k3s single‑node | CircleCI/GitLab Runner         | \~US\$5           | 1 vCPU / 512 MB VM           |
| eu‑central‑1   | k3s single‑node | CircleCI/GitLab Runner         | \~€5              | Same stack                   |
| ap‑southeast‑1 | Docker‑only     | CircleCI runner container      | \~US\$3           | Cheaper but no pod isolation |

> *Clusters are independent*—a down region's jobs are re‑queued and picked up by the next available runner with matching tags.

---

## 6  ARM & Multi‑Architecture Builds

* **Docker Buildx** in CI enables `linux/amd64` *and* `linux/arm64` images from the same workflow.
* Hosted ARM64 exists on GitHub Actions, CircleCI, Semaphore; otherwise register your OWN ARM runners (Raspberry Pi 5, AWS Graviton, etc.).

Example CircleCI job:

```yaml
build:arm64:
  docker:
    - image: docker:24
  steps:
    - setup_remote_docker:
        docker_layer_caching: true
    - run:
        name: Build multi-arch image
        command: |
          docker buildx create --use --name multi
          docker buildx build --platform linux/amd64,linux/arm64 -t registry.example.com/app:$CIRCLE_SHA1 . --push
```

---

## 7  Cost‑Saving Playbook

1. **Prefer CircleCI self‑hosted runners** for CPU‑intensive stages (build, test).
2. **Cap resources** (`cpus`, `memory`) per runner / pod.
3. **Use `needs:` & `concurrency:`** to avoid parallel waste.
4. **Cache dependencies** (npm, pip, cargo) between jobs.
5. **Scale‑to‑zero** runner managers via KEDA or HPA.
6. **Public OSS?** Offload to GitHub Actions unlimited tier.

---

## 8  Monitoring & Alerting

| Signal             | Tool                       | Threshold                          |
| ------------------ | -------------------------- | ---------------------------------- |
| CI queue length    | CircleCI/GitLab Prometheus | > 5 pending jobs triggers scale‑up |
| Runner pod CPU     | kube‑metrics‑adapter → HPA | > 80 % for 5 m                     |
| Build failure rate | Sentry CI integration      | > 10 % failures / day              |

Alert routing via PagerDuty; dashboard in Grafana `ci-cd/main`.

---

## 9  Rollback & Deployment Strategies

| Strategy             | When to use                               | Tooling                                 |
| -------------------- | ----------------------------------------- | --------------------------------------- |
| **Blue‑Green**       | Backend services where cold starts are OK | Kubernetes `service.selector` switch    |
| **Canary**           | Gradual rollout to 10 % traffic           | Istio / Cloudflare Load‑Balancing rules |
| **Instant Rollback** | Static sites & edge functions             | Vercel `vercel rollback <id>`           |

---

## 10  Best Practices Checklist

* [ ] All repos contain a **one‑click setup** `templates/ci.yml` preset.
* [ ] Secrets are stored in **Vault** / GitHub Secrets, never plain YAML.
* [ ] CI statuses block merges unless *all* required jobs pass.
* [ ] Each pipeline finishes in **≤ 10 minutes** (use caching, parallel tests).
* [ ] SBOM published on every release (`cyclonedx‑bom` action).
* [ ] Production deployments are **tag‑triggered** (`v*.*.*`).

---

## 11  Further Reading

* [CircleCI Runner Docker Images](https://hub.docker.com/r/circleci/runner-agent/tags?name=machine)
* [CircleCI Runner Overview](https://circleci.com/docs/runner-overview/)
* [GitLab Runner Helm chart](https://docs.gitlab.com/charts/)
* [k3s Lightweight Kubernetes](https://k3s.io/)
* [KEDA: Kubernetes Event‑Driven Autoscaling](https://keda.sh/)
* [GitHub Actions Runner Resource Limits](https://docs.github.com/en/actions/hosting-your-own-runners/about-self-hosted-runners#using-labels-with-self-hosted-runners)
