
## Free Tier(12mon)

### 1. EC2 instances
> In your first year of opening an AWS account, you get 750 hours per month of t2.micro instance usage (or t3.micro where t2.micro isn't available) when used with free tier AMIs, 750 hours per month of public IPv4 address usage, 30 GiB of EBS storage, 2 million I/Os, 1 GB of snapshots, and 100 GB of bandwidth to the internet.

keypair, VPC/subnet, security groups 都是 region-specific 的

#### Networking
> [这个人的图文教程](https://lxnchan.cn/aws-ipv6.html)很详细，应该也不用但如果别人问到问题可以 refer 

先 enable ipv6

1. 编辑 default VPC, edit CIDR
2. 选定一个 subnet, edit CIDR 
3. 给 VPC 创建 ipv6 出站路由
Navigate to VPC > Route Tables in the AWS Management Console.
Find the route table associated with the subnet where your EC2 instance is located.

Add a default route for IPv6 (::/0) pointing to the Internet Gateway (IGW):
    Click on the route table.
    Go to the Routes tab and click Edit routes.
    Add a new route:

	- destination:  ::/0
     Your Internet Gateway (e.g., igw-xxxxxx).

Save the changes.

之后 security group 别忘了添加 v6 的入站。

1. import keypair
2. create security group


![image.png](https://pimg.nxapi.us.kg/2025/02/287acb4e9e018dfce10912d1cff8a1f8.png)

1.创建第 2 个 ipv6 only 就简单了，只需要选择 keypair, 正确的 subnet, 勾选 enable autoassign ipv6 && disable autoassign ipv4, 选择正确的 volume size 即可。
debian/ubuntu 应该都行

```
echo 'Acquire::ForceIPv6 "true";' | sudo tee /etc/apt/apt.conf.d/99force-ipv6
```
然后常规的 v6 资源访问没问题，可 github 却变成了死胡同。此时有两个办法
1. (据说可行但是没有实验成功)启用 EC2 的 NAT64/DNS64
2. 上 WARP. 用 fcarmen 的脚本就行。注意选择双栈接口，并设置为全局模式。

## Always Free