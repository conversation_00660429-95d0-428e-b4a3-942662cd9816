---
sidebar_position: 1
title: Compute Resources
sidebar_label: Compute
---

# Compute Resources

This document outlines the compute resources used by the Nexus AI team.

## Overview

NexusAI 的计算资源几个原则
- MultiCloud
- Cost Effective
- Multi Region 多区域覆盖。根据区域间 cable connection 的可用性，分布在不同的地域。中国内地之外，主要考虑三个区域：亚太区域、西欧区域和北美区域

## Providers
### Reliability

- Tier 1 

    - GCP Always Free. 只有美国三个区域可以开，200G/mo bandwidth. 计算性能比较弱，感觉 IO 特别弱 
    - AWS Free Trial. 期限一年。需要运行 arm 的 ec2 instance
    - Digital Ocean Free Trial. credit 有效期一年。性能很弱，价格很高，临时对付下是可以的

- Tier 2
    很稳定的私家 VPS, 全年可用度达到 98~99%

    - GGY HKCMI
    - Greencloud. 可用性没有 SLA，但实际使用四年下来，可用性很高
    - RFC (LAX). 性能很弱，只能跑个反代 

- Tier 3
    凑合能用的 VPS, 可用度达到 90%+, 但要认为实际随时会挂

    - justvm
    - virmach

## Usage
- GCP Always Free

    - Oregon: hashicorp vault 
    - NYC: uptime kuma
    - Oregon+NYC
        - chat2api
        - chat-api
        - hashicorp vault
        - Kong Gateway