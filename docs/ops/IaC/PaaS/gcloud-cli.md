# gcloud CLI 实战指南（2025 版）

---
## 安装与升级
### Homebrew（一行式）
```bash
brew install google-cloud-cli           # 首次安装
brew upgrade google-cloud-cli           # 升级最新版
````

> **更新频率**：Google Cloud CLI 月度发布；建议开发机 **每月一次** `brew upgrade` 并运行 `gcloud components update` 确保补丁同步。

### 手动安装（macOS/Linux）

1. 下载最新 [tar.gz](https://cloud.google.com/sdk/docs/install)
2. `./install.sh --quiet`
3. `exec -l $SHELL`
4. `gcloud init`

---

## 核心概念

### Service Account

* 机器身份，格式：`<name>@<project>.iam.gserviceaccount.com`
* 可为其 **生成 JSON key**（离线签名）或使用 **IAM 绑定**（在线元数据）。

**查看预设角色**

```bash
gcloud iam roles list --project=<PROJECT_ID>
# 查看 Compute Admin 角色详情
gcloud iam roles describe roles/compute.admin
```

### Application Default Credentials (ADC)

| 步骤 | 查找顺序                                    | 说明                                                                |
| -- | --------------------------------------- | ----------------------------------------------------------------- |
| 1  | `GOOGLE_APPLICATION_CREDENTIALS` 环境变量   | 指向 JSON key 文件                                                    |
| 2  | Metadata Server                         | GCE/GKE/Cloud Run 等运行时自动注入                                        |
| 3  | `gcloud auth application-default login` | 本地用户凭据 (\~/.config/gcloud/application\_default\_credentials.json) |

> **优势**：同一段代码可在本地、CI、GCE 无缝运行；避免硬编码。

### 重要环境变量

| 变量                      | 作用           | gcloud CLI 优先级 |
| ----------------------- | ------------ | -------------- |
| `CLOUDSDK_CORE_PROJECT` | CLI 全局默认项目   | **最高**         |
| `GOOGLE_CLOUD_PROJECT`  | SDK & 代码默认项目 | 2              |
| `GCLOUD_PROJECT`        | 部分旧库兼容       | 3              |

---

## 使用场景示例：Bootstrapping 项目

以下示例演示 **快速配置** GCP 项目并启用常用 API。

```bash
# 1) 登录（默认浏览器）
gcloud auth login

# 2) 查看并设置项目
gcloud projects list
PROJECT_ID="cosmic-antenna-429409-b4"
gcloud config set project ${PROJECT_ID}

# 3) （可选）设置 Billing/Quota Project
# gcloud config set billing/quota_project ${PROJECT_ID}

# 4) 启用 API
APIS=(aiplatform.googleapis.com run.googleapis.com speech.googleapis.com)
gcloud services enable ${APIS[@]}

gcloud services list --enabled   # double‑check

gcloud auth application-default print-access-token  # 检查当前 ADC
```

---

## 多配置管理

```bash
# 查看现有配置
gcloud config configurations list
# 新建并激活
gcloud config configurations create dev-us
gcloud config configurations activate dev-us
# 配置账号/项目/区域
gcloud auth login
gcloud config set project my‑dev‑project
```

> **实战场景**：一台开发机需同时管理 *个人账号* 与 *公司账号*，用配置隔离浏览器登录状态与默认项目参数。

---

## 账户与凭据管理

```bash
# 撤销指定用户账号
gcloud <NAME_EMAIL>

# 打印 & 撤销 ADC
gcloud auth application-default print-access-token
gcloud auth application-default revoke
```

> **提示**：gcloud CLI Session **同时只维护一个 ADC**，确保脚本前后切换正确配置。

---

## Service Account Key 过期与轮换

### 快速生成并指定有限期限

```bash
VALIDITY="720h"  # 30 天
SA="svc-dev@${PROJECT_ID}.iam.gserviceaccount.com"
FILE="sa-key.json"

gcloud iam service-accounts keys create ${FILE} \
  --iam-account=${SA} \
  --validity-duration=${VALIDITY}
```

> **组织级策略**：`constraints/iam.serviceAccountKeyExpiryHours` 可强制定期轮换。

---

## 综合示例：Vertex AI + Storage + Speech 权限 SA

```bash
SA_NAME="vertex-sa-dev"
DISPLAY="Vertex+Storage+Speech"
PROJECT_ID="neon-coast-425016-k9"

# 创建 SA
gcloud iam service-accounts create ${SA_NAME} \
  --display-name="${DISPLAY}"

# 批量绑定角色
ROLES=(roles/aiplatform.admin roles/storage.admin roles/speech.admin)
for ROLE in "${ROLES[@]}"; do
  gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="${ROLE}"
done

# 生成 Key
gcloud iam service-accounts keys create ${SA_NAME}.json \
  --iam-account=${SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com
```

> 现行最佳实践：仅授予 **最小必要** `roles/aiplatform.user` + `roles/storage.objectAdmin`，避免使用粗粒度 Admin 角色。

---

## Vertex AI 凭据快速获取

```bash
CONF=vertex-cn
PROJECT_ID=my‑vertex‑proj
REGION=us‑central1

#gcloud 配置
gcloud config configurations create ${CONF}
gcloud config configurations activate ${CONF}

gcloud auth login
gcloud config set project ${PROJECT_ID}

# 获取短期 ADC 并写环境变量
export GOOGLE_APPLICATION_CREDENTIALS="$HOME/.config/gcloud/${PROJECT_ID}_adc.json"
gcloud auth application-default login

# 在脚本中使用指定配置
gcloud --configuration=${CONF} ai models list --region=${REGION}
```

---

*进一步阅读*

* [Google Cloud SDK Release Notes](https://cloud.google.com/sdk/docs/release-notes)
* [IAM Best Practices](https://cloud.google.com/iam/docs/best-practices)
* [Service Account Key Management](https://cloud.google.com/iam/docs/creating-managing-service-account-keys)

