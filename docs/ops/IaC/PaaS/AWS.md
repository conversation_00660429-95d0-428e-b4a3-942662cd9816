
# AWS 免费试用 & Always‑Free 全攻略

> **最后校订**：2025‑08‑01  
> **适用范围**：面向内部工程团队（知识库 / Astro Starlight）  
> **本文目标**：
> 1. **保留**原内部草稿的所有要点与实操指南  
> 2. **纠正/更新**因 2025‑07 规则变动而过时的信息  
> 3. **提升可读性**——更清晰的层次、表格、示例  
> 4. **新增**成本监控、限额告警、无公网 IP 上网方案等实践

---
## 快速概览
| 分类 | 关键要点 (2025‑08) |
|------|-------------------|
| **Free Plan** | 仅 *2025‑07‑15* 以后新注册的账户；送 **$100 Credits + 额外任务 $100 Credits**；有效期 **6 个月** |
| **Legacy 12‑Month Free Tier** | 仅老账户（注册于 2025‑07‑14 前）继续享有；涵盖 EC2 750h `t2/t3.micro` 等 |
| **Always‑Free** | 对所有账户永久生效（≈30 项服务）；与 Credits/12‑Month 可叠加 |

---
## 新版 Free Plan（2025‑07 起）
| 内容 | 额度 | 备注 |
|------|------|------|
| **注册即送 Credits** | $100 | 立即可用，6 个月有效 |
| **完成 AWS Builder Lab / Skill Badge** | +$100 Credits | 同样 6 个月有效，按到账日期起算 |
| **适用服务** | 任意按量计费服务 | 包括 SageMaker Studio Lab、Bedrock 等 |
| **退出条件** | Credits 或 6 个月先到即结束 | 之后仅剩 Always‑Free |

> 🔎 官方公告：*Introducing the AWS Free Plan – More Credits, Same Always‑Free* (2025‑07‑15)

---
## Legacy Free Tier（旧账户）
12‑Month Free Tier 仍对 2025‑07‑14 前注册的账号有效，核心资源如下：

| 服务 | 月度免费额度 | 时限 |
|-------|-------------|------|
| **EC2** | 750 h `t2.micro` (x86) / `t3.micro` (ARM) | 12 个月 |
| **EBS** | 30 GB 通用型 gp2 + 2 M I/O | 同上 |
| **RDS** | 750 h `db.t3.micro` (MySQL/PostgreSQL) + 20 GB 存储 | 同上 |
| **ElastiCache** | 750 h `cache.t3.micro` (Redis) | 同上 |
| **OpenSearch** | 750 h `t3.small.search` + 10 GB 磁盘 | 同上 |

> *注意：* 新账户 **只** 有 Free Plan + Always‑Free，不再提供 12‑Month Free Tier。

---
## Always‑Free 配额清单
以下配额 **终身有效**，与账龄、Credits 无关；超额后按标价计费。

| 服务 | 免费额度 (月) | 关键限制 |
|------|---------------|---------|
| **Lambda** | 1 M 请求 + 400 k GB‑秒 | 最长 15 min；包含异步重试 |
| **API Gateway** | 1 M REST 调用 | 不含 WebSocket/HTTP API |
| **S3** | 5 GB 标准存储 + 20 k GET + 2 k PUT | Glacier, IA 不在免费内 |
| **DynamoDB** | 25 GB 存储 • 25 RCU • 25 WCU | 相当于 200 M 读 + 25 M 写/月 |
| **CloudFront** | 1 TB 全球出网 + 10 M 请求 | 含 HTTP/HTTPS |
| **Data Transfer Out (DTO)** | 100 GB AWS Region → Internet | 跨 AZ/VPC 不计 |
| **CloudWatch** | 10 自定义指标 • 5 GB Log Ingest | Alarm 10 个 |
| **SNS** | 1 M Publish + 1000 SMS (US) | SMS 限美国 |
| **SQS** | 1 M Requests | FIFO 队列记两次 |
| **Glue Data Catalog** | 1 M Objects | Glue ETL 不免费 |
| **KMS** | 20 k 请求 | 含加解密、生成数据密钥

*(完整列表见 [AWS Always Free](https://aws.amazon.com/free/services) 页面)*

---
## T4 free offering for a limited time
2025.12.31 之前，[T4 系列](https://aws.amazon.com/ec2/instance-types/t4/)免费。过去几年一直在续这个政策，每年都关注吧

---
## 网络流量免费额度
1. **入站 (Internet → AWS)**：永久免费。
2. **出站 (Region → Internet)**：首 **100 GB/月** 免费（所有服务累计）。
3. **CloudFront**：额外 **1 TB/月** 全球出网免费；不占 DTO 额度。
4. **跨 AZ / 跨 Region**：不在 Always‑Free 范围，按 EC2 Data Transfer 标价计费。

---
## IPv4 地址计费变化
| 生效日期 | 政策 | 影响 |
|----------|------|------|
| **2024‑02‑01** | 每弹性 IPv4 地址 **$0.005/小时**（首个免费） | EC2、RDS、ELB 均计费 |
| **2025‑07 追补** | Free Plan / Legacy 用户：仅首 IPv4 免费 | 建议转 IPv6/私网 |

**成本规避策略**
- Launch Instance 时 *Disable* Auto‑assign IPv4。  
- 利用 **PrivateLink** / **NAT Gateway (按流量)** 或自建 WireGuard 隧道。  
- 静态内容用 CloudFront + ACM 自签域名，避免直接暴露实例。

---
## 成本与配额监控
### 1. 成本监控
| 工具 | 作用 | 频率 |
|------|------|------|
| **Cost Explorer** | 图形化查看历史 & 预测 | 日 / 小时级 |
| **Budgets** | 设阈值 → 邮件/SNS 告警 | 日 / 实时预测 |
| **Cost Anomaly Detection** | 机器学习检测突增 | 逐小时 |

### 2. 配额监控
1. **Service Quotas → 选服务 → Utilization**（UI）。  
2. **CloudWatch Usage Metrics**：`AWS/Usage` → 创建 Alarm (`>= 80%`).  
3. **CLI/SDK**：
   ```bash
   aws service-quotas get-service-quota \
     --service-code lambda --quota-code L-548AE8D3
````

---

## 实用 Tips & Best Practices

1. **优先用 Serverless**：尽可能用 Lambda + API Gateway + DynamoDB，全部落在 Always‑Free 区。
2. **缓存 / 压缩**：S3 + CloudFront 边缘缓存，大幅减少 DTO。
3. **分账管理**：一账号一项目，预算、警报、权限更清晰。
4. **无公网 IP 上网**：

   * WireGuard 隧道（参见下文脚本）
   * SSM Session Manager 代理 (`--session-manager`)，免开放 22 端口。

---

## CLI 示例 & 自动化脚本

### A. 查询 Free Tier 用量

```bash
aws ce get-cost-and-usage \
  --time-period Start=$(date -d "$(date +%Y-%m-01)" +%F),End=$(date +%F) \
  --granularity MONTHLY \
  --filter '{"Dimensions":{"Key":"RECORD_TYPE","Values":["FreeTier"]}}' \
  --metrics "UnblendedCost"
```

### B. 创建月度预算并告警

```bash
aws budgets create-budget \
  --account-id $ACCOUNT \
  --budget 'BudgetName=Team-Lab,TimeUnit=MONTHLY,BudgetType=COST,BudgetLimit={Amount=20,Unit=USD}' \
  --notifications-with-subscribers 'Notification={NotificationType=ACTUAL,ComparisonOperator=GREATER_THAN,Threshold=80,ThresholdType=PERCENTAGE,NotificationState=ALARM},Subscribers=[{SubscriptionType=EMAIL,Address="<EMAIL>"}]'
```

### C. WireGuard 隧道 (无公网 IP)

```bash
# EC2 (Has Public IP) /etc/wireguard/wg0.conf
[Interface]
Address = *********/24
PrivateKey = <srv_private>
ListenPort = 51820

[Peer]
PublicKey  = <cli_public>
AllowedIPs = *********/32

# EC2 (No Public IP) /etc/wireguard/wg0.conf
[Interface]
Address = *********/24
PrivateKey = <cli_private>

[Peer]
PublicKey  = <srv_public>
Endpoint   = <srv_eip>:51820
AllowedIPs = 0.0.0.0/0
PersistentKeepalive = 25
```

---

## 参考链接

* [AWS Free Plan FAQ](https://aws.amazon.com/free)
* [AWS Always‑Free Services List](https://aws.amazon.com/free/services)
* [AWS Cost Explorer](https://docs.aws.amazon.com/cost-management/latest/userguide/ce-what-is.html)
* [Service Quotas](https://docs.aws.amazon.com/general/latest/gr/aws_service_limits.html)
* [Elastic IPv4 Address Pricing](https://aws.amazon.com/ec2/pricing/on-demand/#IPv4_Addressing)
* [Blog: Data Transfer Out 100 GB / CloudFront 1 TB Free](https://aws.amazon.com/blogs/aws/aws-free-tier-data-transfer-expansion-100-gb-from-regions-and-1-tb-from-amazon-cloudfront-per-month/)

