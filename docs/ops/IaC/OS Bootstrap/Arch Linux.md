看起来是个"很专业"的 linux, 包管理器用 pacman, dependencies 很详细，速度快。
我目前先在 dev2 上练手。

## Concepts
- [Meta package and package group](https://wiki.archlinux.org/title/Meta_package_and_package_group#Difference_between_meta_package_and_package_group)

## Installation
- mount Live CD
- 原则上运行 `archinstall` 即可进入 console 的 installation wizard.
- 重要的选项有 
	- disk. 必须。见下
	- timezone
	- create user 
	- hostname
	- profile. 选择安装类型，minimal 就真是 minimal, 以后还是选择 server 吧
- 完成后建议还是进入 chroot enviroment 查漏补缺，免得像我一样连网络都没有

minimal installation + git/lftp/vim 最后占用 2.4G 空间，还是显著的比 `alpine` 吃资源的

### disk partitioning 失败
在 `dev2` 执行 disk partitioning 一直失败，我都使用了最简单的缺省两个分区的配置而已，fs 配置都是缺省值
- brtfs
- use compression
- do NOT use subvolumes

解决办法：
1. 先在 wizard 里选择缺省的 partition table, 尽管失败，它还是会创建 p table 的。如果不愿意失败，就用 `fdisk` 创建两个分区，一个 EFI 一个 root partition. EFI `+512M` 即可
2. format. 假设 VPS 的磁盘设备是 /dev/vda
```
mkfs.fat -F32 /dev/vda1

# use ext4
mkfs.ext4 /dev/vda2
# use brtfs with compression
mkfs.brtfs -f /dev/vda2

```
3. mount
```
mount /dev/vda2 /mnt

# mount EFI
mkdir /mnt/boot
mount /dev/vda1 /mnt/boot
``` 
1. 最后在 wizard 里选择 `use prepartitioned...` 选择 root partition `/mnt`

### network
如果安装完成后直接重启进入系统没网络，大概率这是连 dhcpcd 都没有。就得用缺省的 `systemd-networkd`

```
sudo systemctl start systemd-networkd
sudo systemctl enable systemd-networkd

# /etc/systemd/network/20-wired.network
[Match]
Name=enp0s3

[Network]
DHCP=yes
DNS=*******
IPv4Forwarding=yes

sudo systemctl restart systemd-networkd

# 重启后要多等待一会儿
```

`ip link set enp0s3 up`
`sudo sed -i 's/^nameserver .*/nameserver *******/' /etc/resolv.conf`

如果还不行，就得手动配置了，类似

```
sudo ip addr add *************/24 dev enp0s3

sudo ip route add default via ***********

echo "nameserver *******" | sudo tee /etc/resolv.conf
```

## System Config
### user
```Bash
pacman -S sudo
useradd example_user
usermod --append --groups wheel example_user
EDITOR=vim visudo

%wheel ALL=(ALL) ALL
%wheel ... NOPASSWD ALL
```
注意，如果在 installation wizard 中创建用户并且赋予了 sudo 权限，它会在 `/etc/sudoers.d` 创建一个文件，让这个用户每次都 prompt password. `sudoer` 文件的 rule parsing 是后面的优先。
所以我后来直接把 /etc/sudoers.d 里的文件删掉了。

### update
```
pacman -Syyu 
pacman update
```

### desktop
在 parallels 安装 KDE 的方法在[飞书](https://paratrips.feishu.cn/wiki/FgmywfWBTiFrmAkol4acKYtVned?from=from_copylink)；服务器端如果需要可以用 xfce4

`pacman -S xorg-server xfce4 xfce4-goodies lightdm lightdm-gtk-greeter lightdm-gtk-greeter-settings`

不知道怎么设置缺省不进入桌面 greetings. 

### shell
```
sudo pacman -S bash-completion

```

```
bash <(curl -fsSL https://sing-box.app/arch-install.sh)
```

### networking
```
echo "net.ipv4.ip_forward = 1" | sudo tee /etc/sysctl.d/30-ipforward.conf
sudo sysctl -p /etc/sysctl.d/30-ipforward.conf
```

