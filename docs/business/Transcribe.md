---
sidebar_position: 5
title: CP on Transcribing
---


## my 设计原则
核心是理解，然后是深入，social(content) research 的工具。对大众语料进行分析，取其精华。既不是一个专业严肃的 research 工具，也不是泛泛的象征性的看个总结概要了事。

-  domain optimization. 对特定领域、类型的语料做优化。这些领域是我所关注的，或者说我想深入探索，并且将来能够挂接专业服务做 monetize 
- 特定语言。强化特定语言人群，JP, Thai, Indo-Malay，Spanish, Portugese(在日巴西人很多，一起加强拉丁美洲的覆盖)，可能加上 HK Chinese. 
- 社交传播。内容社交(内部分享、follow)不要，太复杂，也没有意义；但是需要强化 snippet 的分享，参考 snipd. 
- 探索发现。类似 pplx 的 discover. 这里需要探索用户心理，pplx 现在有幻觉，AI 搜索的宿命，但是我一点都不索引也不现实，否则怎么提供 discover? 只靠谈定平台的 ranking 吗？那是不是太 biased。 ig/ youtube/podcast/ reddit/ x ... 综合 compile 出来。

## meeting notes
特点是集成 team/enterprise/personal calendar, 管理会议，也具备上传 audio notes 转写的能力。
- 转写实录
- speaker diarization
- share or other extensive team collaboration 

### list
- Otter 应该是业界王者。提供免费计划，支持 email 注册，3 免费 audio uploads; 支持 7 天高级别 plan trial. 交互界面更像是会议管理的 slack. ![image.png](https://pimg.nxapi.us.kg/2025/01/d0441436182ef1312f06f79d0217a4ba.png)

- [Grain](https://grain.com). gaining popularity. 提供免费计划，只能 Google 注册，要求 calendar 权限, 20 个 audio uploads，不支持 free trial。提供 `Coaching` `Insights` 等深度内容管理能力。
	`Stories are collections of Clips you can stitch together to summarize a meeting, compile feedback, tell a narrative, and more.`
	用两个 podcast 试用下来认为 speaker diarization 更胜一筹。但二者都是原文忠实转写，不存在领域优化。比如 VO2Max, 都写成 `VO two max` 总体能 word to word 对应 audio track. 
- [Dovetail](https://dovetail.com/). `The fastest way to understand your customer` 它的 highlights 很有意思，是直接 compile 成一个更短的 snips 的结合。比如我上传的 `running to Heart rate made easy` 原文 9 分钟，经过大约 40 秒，处理成了一段 2m29s 的精简版。当然，我觉得这个功能太难控制质量了，不同人关心的内容当然不同了。他现在只能从 key highlights 搜寻，其实最好的还是 AI customized summarization 然后 TTS 出来。至于 notebookLM 那种，我认为 overkill 了，很容易引发爆点，但是重点还是**个性化的 insights** 
- [fellow](https://fellow.app/). 还没试用
- [Fireflies.ai](https://fireflies.ai/) 也是重视 call to action. 提供[免费计划](https://fireflies.ai/pricing)，号称 free transcribing forever(shared bot meetings, mobile apps 上的实时语音 etc)。只能 Google/MS 账户登录，提供 android/iOS app

他们在 UI 交互上都各有鲜明的特色，定位迥异。

## voice memo
- [VOMO](https://apps.apple.com/us/app/vomo-transcribe-meeting-notes/id6449889336?pt=126411129)
- vibe. 开源

## summarization
如下来自 [reddit](https://www.reddit.com/r/ChatGPT/comments/16db0zp/best_ai_for_summarizing_articleslong_reddit_posts/)  总结的不错。recall 确实是在质量上下了功夫的，看起来。不像其他的应该只是非常简单的一个 prompt agent，使用了知识库，使用了更复杂的前端渲染。
1. [Recall](https://www.getrecall.ai/?t=16db0zp) produced the best quality summaries over a wide range of formats (news, blogs, video). It has a browser extension which makes it convenient to use and it also has a knowledge base where you can save the summaries if you want to come back to them later.
    
2. [Glasp](https://glasp.co/ai-summary) is a really neat tool - it also has a browser extension and can create summaries of a wide range of web pages. What sets it apart is its social aspect where you can share your web highlights and notes from any webpage with other users.
    
3. [Jasper](https://www.jasper.ai/?t=3) created high-quality summaries and has plenty of features focussed towards marketers. Has a good user-friendly interface, multiple language options, and integrates with other writing tools. Downside is it's priced as a B2B tool so it's quite expensive.
    
4. [QuillBot](https://quillbot.com/) is simple to use and the best part is it’s free. I use it if I quickly need to paraphrase something but it's not the best for longer form content.
    
5. [TLDR This](https://tldrthis.com/) is another browser extension with automatic summarization with a single click. I would have ranked this higher because of its straightforward UI but on multiple pages it didn’t work for me.
    
6. [Wordtune](https://www.wordtune.com/summarizer) is a very polished product and its browser extension offers features for writing content as well as summarizing it. It is focused on helping professionals get more done, faster.
    
7. [Gemini](https://gemini.google.com/app) does a great job as an AI article summarizer - having full control of the prompt gives you more control of the summary. The down side is not having a browser extension like most of these other tools so you have to copy and paste and write your prompt each time.

## social media clipping

- opus clip