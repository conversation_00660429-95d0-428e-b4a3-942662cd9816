

## 关注方向

1. 核心框架技术。跑业务逻辑的
   - LLamaIndex.  the leading data framework for building LLM applications
   - Langchain. 仍然不容忽视
   - haystack. 发展历史很久，而且在 deepest 有生产级应用。[cookbooks](https://github.com/deepset-ai/haystack-cookbook?tab=readme-ov-file)  [Tutorials](https://haystack.deepset.ai/tutorials)
   - AutoGPT. github star 最多，但是不太明白为啥似乎声势甚微，网站 blog 也都没啥技术活儿。
2. 大厂 agentic framework
   1. [Autogen](https://github.com/microsoft/autogen). ms 出品，开源。[项目站](https://microsoft.github.io/autogen/)
   2. GCP Agent Builder. 2024 Apr 才出来
3. LLM Developing
   1. HuggingFace. 必须要学会 finetune, 如何应用 LoRA
   2. OpenWebUI. 是个 UI 但是源自 Ollama
   3. xInference. 一站式推理后台。集成了 vLLM/sglang/HF 等
4. 数据底层
   - DataBricks. 头牌
   - [mindsdb](https://github.com/mindsdb/mindsdb). 其实也带有一定的 Ops 属性，但是我理解重心还是在 LLM 赋能Data，无论是 RAG 还是 fine tune
5. 爬虫。应该算作数据类别的子类？供应 raw data. 
   - Firecrawl. OSS 版本 1.0 了
   - spider.dev. 
   - ScraphgraphAI 
1. RAG
   1. dify . 工作流编排
   2. RAGFlow. 据说精准度更好，但是我看代码就。。不太生产级。不直接支持 arm 部署，很简单的
   3. [FastGPT](https://github.com/labring/FastGPT) 令人鄙视的 PHP 项目，但是无奈人家活得久做的早，据说效果还是不错
   4. AnythingLLM
2. Playgrounds
    1. Google Colab. 有白嫖 T4 的额度，22G VRAM, 能跑个 12b 的模型。免费额度模糊，目前目测是每周 ~4h. 
    2. kaggle. Google 收购了，dataset host, 也提供 notebook hosting, GPU 容器的免费额度更充裕。
    3. Deepnote. 不提供 GPU, 但 CPU/mem 给的挺多


### LlaMaIndex

Resources from https://slides.com/seldo/rag-and-agents

- [5 line starter](https://bit.ly/li-starter)
- [Router Query Engine](https://bit.ly/li-router-query-engine)
- [Chat Engine](https://bit.ly/li-chat-engine)
- [Sub Question Query Engine](https://bit.ly/li-sub-question-query-engine)
- [Basic ReAct agent](https://bit.ly/li-agents)
- [ReAct in action](https://bit.ly/li-react)
- [Structured Planning Agent](https://bit.ly/li-structured-planning-agent)
- [Language Agent Tree Search](https://bit.ly/li-lats) (LATS)
- [Instrumentation](https://bit.ly/li-instrumentation)
- [Step-by-step control](https://bit.ly/li-agent-runner)
- [Custom agents](https://bit.ly/li-custom-agent)



## 实施路径

首先还是要更多借助 serverless 和 IaaS 云。

### AWS

SageMaker



### GCP



### 其他

cloudflare, github



## Work Ideas

> 无论是练手小项目，还是实验，还是产品化的前奏

###  1. youtube/podcast data insighter



1. Database. databricks, 到底如何存储批量数据，以用于批量分析。
2. AWS API Gateway + Lambda 整一个新的 API 
3. CloudFlare Worker as AI Gateway  还是得用 ts 写。后端需要增加 d2a, c2a, LiteLLM  可能需要一天时间。关闭 doams, dosgp
4. Lobe 的 server db 端
5. RAG 系统。对照今天收藏的公众号文章，RAGFlow, dify, flowise, langchain, llamaindex
6. LLMOps. dify, langchain, autoGPT. 多参考 youtube



最后，v0 + cursor 玩儿吧。

目标

- 指定话题，跟踪热点，总结自动生成 digest, 可选 audio 输出



