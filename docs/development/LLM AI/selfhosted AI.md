---
sidebar_position: 2
label: selfhosted
sidebar_label: selfhosted AI
---
## Overview

 私有化部署、开发的方方面面。
 - 训练，特别是后训练。包括 finetune, model card info, distill 等
 - 本地化模型部署、服务
 - 硬件管理

## Hardwares

### quick facts
N 卡肯定是最好的，但是散热、噪音、能耗、配套的网络管理，都很麻烦。


- 2x 4090+64/128G RAM 可能跑得动 deepseek r1 70b Q4, 大约 
- llama.cpp 调试一些参数，3090 可以跑起来 ds-r1-70b-Q1.58, 大约 2 token/s
- 目前 apple silicon 能耗出众，但是推理能力、prompt processing 还是很弱。性价比最高的是 M2 Ultra, 主要是带宽大。
- 满血 ds r1 671b-Q8. 用 AMD MI300x(价值大约 230 万)，最大速度 3500 token/s. 内存 1.5T
![image.png](https://pimg.nxapi.us.kg/2025/02/de0499201371ddac4282b11447df7a19.png)

## Software Suites
无论如何， 先学习 [llama.cpp guide](https://blog.steelph0enix.dev/posts/llama-cpp-guide/), 这部分 [list of LLM configuration options and samplers](https://blog.steelph0enix.dev/posts/llama-cpp-guide/#list-of-llm-configuration-options-and-samplers-available-in-llamacpp)  哪怕是用公域模型开发也是 gold info. 

[Dynamic Temperature](https://www.reddit.com/r/Oobabooga/comments/191klr8/some_information_about_dynamic_temperature_added/)
[Which GGUF is right for me?](https://gist.github.com/Artefact2/b5f810600771265fc1e39442288e8ec9)
[Setting Top-K, Top-P and Temperature in LLMs](https://rumn.medium.com/setting-top-k-top-p-and-temperature-in-llms-3da3a8f74832)

### llama.cpp
性能最佳。

`brew install huggingface-cli`

### Ollama

### vLLM




## Cloud Deployment
### Providers
- Fireworks.ai

> If you are working with a rather popular model, like Mixtral or Llama 3, want to fine tune a LORA/QLORA adapter and dont need to add some custom serving logic, check out [Fireworks AI](https://fireworks.ai) - you only pay for data used in fine tuning, can swap out adapters (so multiple tunes) without paying for either storage, network or idle. You only pay for tokens(both fine tuning and serving, serving tokens cost the same as the base model, fine tuning tokens are priced differently)!
	
> If you really need that flexibility, work with a model not provided there or need to work with the model layers themselves, not LORAs - go for google cloud and spot instances, I have yet to find a better deal!

- Google Cloud. 不同于常规认知，不考虑 setup 的 time cost, 好像普遍认为 Google 是最优价格
- [SkyPilot](https://github.com/skypilot-org/skypilot) - [Docs](https://skypilot.readthedocs.io/en/latest/docs/index.html) Build, deploy, and monitor ML models. it's also a platform for MLOps with selfhosted AI.
- runpod
- [vast.ai](https://vast.ai) 租 GPU
- bentoML  新用户送 $10, 基本上没啥用
- Google Colab 每个月可以白嫖一阵 T4 
- Kaggle 

- 中国的
	- AutoDL
	- [Featurize](https://featurize.cn/vm/available)  比 AutoDL 便宜

## Resources
- reddit  /LocalLlaMa
- 著名的 [toppc](https://toppc.ca/) 硬件资讯
- [local apps & hardware on HF](https://huggingface.co/settings/local-apps)  