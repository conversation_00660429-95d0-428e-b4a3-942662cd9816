---
sidebar_position: 1
title: API Hub
sidebar_label: Nexus Fabric
---

# API Hub

> codename `nexus-fabric` 能不能简化点

取名来源

- Nexus

  * Central Point: A nexus acts as a focal point or hub where different things come together.
  * Connection: It highlights the relationship and interdependence between the elements it connects.
  * Convergence: A nexus signifies a place where different paths, ideas, or systems meet and interact.
- Fabric

  - A fundamental layer that provides support and connectivity for other components or systems.
  - It represents the underlying infrastructure upon which other things are built.

## Nexus API Suite(planning)

- Youtube manipulation. 
  - 下载字幕， article 化的
  - audio track 
  - whisper 
- Data Analysis
  - /v1/yt/trend_predict?daterange=?&platform=?&....
- weather forecast
- Event query
- LLM Chat



在 Model Routing 这个设计 principal 上，OpenRouter 跟我的思路比较接近

## Purpose

实现一个稳定、可靠、能灵活控制权限的 LLM API 聚合接口，作为 nexusAI 所有产品服务的 LLM 能力基座

- API 转发服务。路由请求到合适的 model providers, 这些 providers 的 SLA 服务能力不同，我要最大限度确保可用性，目标是 99.999 级别
- 本地部署的模型接口。目前我并不具备足够的计算资源
- 提供 LLMOps ——类似 dify 的开发平台——的接口，包括 Agent 
- Workflow. 可以理解为简单的应用？营销文案生成、音视频转写等

## Tech

### 架构

1. Gateway. basic auth, load balance, ratelimit, caching
2. Orchestration
3. Providers



```mermaid
graph TD
    classDef edge fill:#FF9999,stroke:#333,stroke-width:2px;
    classDef gateway fill:#99FF99,stroke:#333,stroke-width:2px;
    classDef auth fill:#9999FF,stroke:#333,stroke-width:2px;
    classDef orch fill:#FFFF99,stroke:#333,stroke-width:2px;
    classDef adapter fill:#FF99FF,stroke:#333,stroke-width:2px;
    classDef provider fill:#99FFFF,stroke:#333,stroke-width:2px;
    classDef obs fill:#FFCC99,stroke:#333,stroke-width:2px;

    A[Client Request]
    B[Edge Layer]
    C[API Gateway Layer]
    D[Auth Layer]
    E[Orchestration Layer]
    F[Model Adapter Layer]
    G[Provider Layer]
    H[Observability Layer]

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    H -.-> B & C & D & E & F & G

    class B edge;
    class C gateway;
    class D auth;
    class E orch;
    class F adapter;
    class G provider;
    class H obs;

```



1. **Edge Layer**
   - Purpose: Global distribution, initial security, and caching
   - Examples: Cloudflare, Fastly, AWS CloudFront
   - Open-source alternative: Nginx with GeoIP module
2. **API Gateway Layer**
   - Purpose: Request routing, basic auth, rate limiting
   - Examples: Kong, Tyk, AWS API Gateway
   - Open-source: Express Gateway, KrakenD
3. **Authentication & Authorization Layer**
   - Purpose: Advanced auth, token management, access control
   - Examples: Auth0, Okta, Keycloak
   - Open-source: ORY Hydra, Casbin
4. **Service Mesh Layer** (Optional)
   - Purpose: Service-to-service communication, advanced routing
   - Examples: Istio, Linkerd, Consul Connect
   - (This layer might not be necessary for your initial setup but could be useful as you scale)
5. **Orchestration Layer ******(Optional)******
   - Purpose: Workflow management, service composition
   - Examples: Apache Airflow, Temporal, AWS Step Functions
   - Open-source: Camunda, Zeebe
6. **Model Adapter Layer**
   - Purpose: Normalize interactions with different LLM providers
   - This is likely a custom layer you'll build
   - Could be inspired by projects like LiteLLM or LlamaIndex
7. **Provider Layer**
   - Purpose: Actual LLM services
   - Examples: OpenAI API, Anthropic Claude, Hugging Face Inference API, Ollama
8. **Observability Layer**
   - Purpose: Monitoring, logging, tracing
   - Examples: Datadog, New Relic, Elastic Stack (ELK)
   - Open-source: Prometheus + Grafana, Jaeger





- about orchestration layer

The Orchestration Layer is responsible for managing complex workflows and coordinating interactions between different services or API calls. It's particularly useful when you need to combine multiple API calls or services to fulfill a single request.

Here's a more detailed explanation:

**Purpose of the Orchestration Layer:**

1. **Workflow Management:** Handles multi-step processes that involve multiple services or API calls.
2. **Service Composition:** Combines results from multiple services to create a unified response.
3. **Error Handling and Retries:** Manages failures in individual steps and implements retry logic.
4. **State Management:** Keeps track of the state of long-running processes.

**Examples in the Context of Your LLM API Hub:**

1. **Multi-Model Queries:** A request might require calling multiple LLM models in sequence. For example:
   - Step 1: Use a classification model to determine the language of the input.
   - Step 2: Based on the language, route to the appropriate translation model.
   - Step 3: Send the translated text to a general-purpose LLM for processing.
2. **Fallback Mechanisms:** If a primary LLM provider is unavailable or returns an error, the orchestration layer could automatically retry with a backup provider.
3. **Complex AI Workflows:** For tasks like content generation that might involve:
   - Step 1: Generate an outline using one model.
   - Step 2: Expand each section using another model.
   - Step 3: Summarize the result using a third model.
4. **Asynchronous Processing:** For long-running tasks, the orchestration layer can manage the process, allowing the client to check back for results later.



backend services

- Models

- storage 

- yotuube data api



REST/gRPC/GraphQL/HTTP, not via SDK





```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant Orchestration Layer
    participant Model A
    participant Model B
    participant Model C

    Client->>API Gateway: Request complex task
    API Gateway->>Orchestration Layer: Forward request
    Orchestration Layer->>Model A: Step 1
    Model A-->>Orchestration Layer: Result 1
    Orchestration Layer->>Model B: Step 2 (using Result 1)
    Model B-->>Orchestration Layer: Result 2
    Orchestration Layer->>Model C: Step 3 (using Result 2)
    Model C-->>Orchestration Layer: Final Result
    Orchestration Layer-->>API Gateway: Compiled Response
    API Gateway-->>Client: Final Response

```





#### AP 区内的请求示例

1. without GeoDNS

```mermaid
graph TD
    A[Client in Bangkok] -->|HTTPS| B[Caddy SSL Termination]
    B -->|HTTP| C[Docker Swarm Routing Mesh]
    C -->|Round Robin| D[Kong SG]
    C -->|Round Robin| E[Kong HK]
    C -->|Round Robin| F[Kong Tokyo]
    D & E & F -->|Forward Request| G[Backend Service]

```

2. With GeoDNS

```mermaid
graph TD
    A[Client in Bangkok] -->|DNS| B[GeoDNS]
    B -->|Route to Nearest| C[Caddy SG]
    B -->|Route to Nearest| D[Caddy HK]
    B -->|Route to Nearest| E[Caddy Tokyo]
    C --> F[Kong SG]
    D --> G[Kong HK]
    E --> H[Kong Tokyo]
    F & G & H -->|Forward Request| I[Backend Service]

```



### Gateway Solutions

1. CloudFlare Workers. 生产环境的首选

```mermaid
graph TD
    A[Client] --> B[Cloudflare Edge]
    B --> C[Cloudflare Workers - API Gateway & Orchestration]
    C --> D[Regional Service Cluster - AP]
    C --> E[Regional Service Cluster - EU]
    C --> F[Regional Service Cluster - NA]
    C --> G[Fallback Global Service]
    
    style B fill:#ccf,stroke:#333,stroke-width:2px
    style C fill:#fcf,stroke:#333,stroke-width:2px
    style D fill:#cfc,stroke:#333,stroke-width:2px
    style E fill:#cfc,stroke:#333,stroke-width:2px
    style F fill:#cfc,stroke:#333,stroke-width:2px
    style G fill:#fcc,stroke:#333,stroke-width:2px

```





2. selfhosted Kong Gateway. 独立路径，k8s 上测试 cloudnative solution, 也可以用于实现 mini apis. 部署很轻量

3. AWS API Gateway. 仅仅用于 origin backends 是 AWS 生态内的服务，如目前的 youtube trending collector, 已经明确用 Lambda 部署



### Endpoint Deployments

对团队和伙伴成员提供试用或工作接口，所以也许是通过外层嵌套一个有计费、鉴权能力的 gateway;



大体想法如下，假设 `baseDomain`  为 `nxapi.lvtu.in` 如下描述 endpoint 的设计名称(baseSubdomain) + baseDomain 即为最终 API endpoint domain 

1. OpenAI 兼容的 LLMapi: baseSubdomain = genai  这个服务面向全球请求来源，自动分配邻近节点和资源，提供最大限度的可用性

   但是，它对于 proximity 的判断不一定是准确的，也不一定能根据实际 backend model providers 的特性提供最好的 request routing。所以我设置三个独立的 regional endpoint

   1. 亚太区区域节点：genai-ap
   2. 欧洲区区域节点：genai-eu
   3. 北美区区域节点：genai-na
   4. 中国大陆区域节点：genai-cn  主要提供国内大模型的接入点

   提供 API 包括 OAI 大部分(理论上要提供全部) API endpoints. chat, transcribe, translate, embeddings, moderation ... 但实际上目前的客户端也不总是需要请求 OAI 的 embeddings 了

2. Anthropic 兼容的 LLMapi. `genai-claude` 面向全球请求来源，自动分配节点和资源，不保证最大限度的可用性，只提供 chat completions 

3. 国内模型的聚合接口 `genai-cn` 因为国内模型的 api platform 大多只有国内的接入点，目前所知只有 deepseek 部署了全球分区节点，但非常少

| region | Server name | endpoint url          | server host   | Resources            |                                                     |
| ------ | ----------- | --------------------- | ------------- | -------------------- | --------------------------------------------------- |
| NA     | genai-na    |                       | Gateway       |                      |                                                     |
| NA     | genai-na0   | genai-na0.nxapi.us.kg | la3           | dev                  |                                                     |
| NA     | genai-na1   | genai-na1.nxapi.us.kg | GCP SC-1      |                      | aiven mysql                                         |
| NA     | genai-na2   | genai-na2.nxapi.us.kg | GCP Iowa-1    |                      | aiven mysql                                         |
| NA     | genai-na3   | genai-na3.nxapi.us.kg | GCP Oregon-1  |                      | 可能放 Kong 吧                                      |
| CN     | genai-cn    |                       | Load Balancer | 国内模型最快的接入点 |                                                     |
| EU     | genai-eu    | genai-eu.nxapi.us.kg  | Gateway       |                      |                                                     |
| EU     | genai-eu0   | genai-eu0.nxapi.us.kg | WHAMS         |                      | 居然 gemini 和 vertex 都不能用, warp代理也不行      |
| EU     | genai-eu1   | genai-eu1.nxapi.us.kg | Just AMS      |                      | aiven mysql                                         |
| AP     | genai-ap1   |                       | HKCMI         |                      |                                                     |
| AP     | genai-ap2   |                       | GC Tokyo      |                      |                                                     |
| AP     | genai-ap3   |                       | AWS JP1       |                      | aiven mysql                                         |
| AP     | genai-ap4   |                       | singapore     |                      | sk-M2X655Hi03w8zx2HDd0e756cB9174d74858b8b0a8f71586f |

### Security

- 服务安全。WAF
- 权限管理。**RBAC** 用 user/group 管理 roles, 然后对应不同的权限，包括 rate limit, model resources, etc.  
- API Key Rotation
- Expiry



### Gateway Features

在 `genai` API endpoint 的 backend, 每个 model provider 提供的就只有一个或者多个 API endpoint, 分别提供不同的响应职能。比如

- c2a 这个 microservice 提供 /v1/chat/completions ，model 支持 gpt-4o, gpt-4o-mini, 单个 microservice 的 api endpoint 限速每分钟 3 次
- uuapi 这个 microservice 提供 /v1/chat/completions, /v1/translate 等多个 endpoint, model 支持 gpt-4o, chatgpt-latest, whisper-1 等。限速每分钟 10 次

等等



所有 genai backend 层级的 providers microservice, 都不需要设计费用控制，但可能需要有 ACL 

- IP based allowList, denyList
- 多类型的 auth key(Bearer xxx 方式的验证)
- Key 作为一条 rule 的有 expiration date 



 `genai` 作为一个较为完善的 API Gateway, 可能需要有能力

- Load Balance
- 完整的 AAA, 其中 Auditing 方面需要用到现代化的 monitor & logging 的技术



#### API Gateway 选项

| Name                     | Price                    | Features                                                     | Extensibility                                      | License |      |
| ------------------------ | ------------------------ | ------------------------------------------------------------ | -------------------------------------------------- | ------- | ---- |
| CloudFlare API Gateway   | Pro  以上，价格不透明(?) | 应该是完整的功能，但同样的，深度绑定 CF 生态                 | 不知道                                             |         |      |
| CloudFlare Workers as... | 至少 Workers Paid+PAYG,  | 需要借助 KV/DB 等自己开发所有 full-fledge API Gateway Features | 肯定足够强，但是受限于 Worker 的执行空间和 runtime |         |      |
| AWS API Gateway          | 1M Req/mo for free-tier. | 深度绑定 AWS 生态。Security 有 WAF/CloudFront 提供,Auth 有 IAM 提供, backend services 有 Lambda, Step, SQS 等, networking 有 VPC Link | 强                                                 |         |      |
| Kong Enterprise          | No freetier              | Full                                                         | Lua 写 plugin, 基本上还是比较简单的                |         |      |
| Kong Gateway             | Free selfhosted          | Full                                                         |                                                    | OSS     |      |
| Zuplo                    | free tier                | Edge, Dev Extensible.                                        | TS 开发                                            |         |      |



### 接口规范

RESTful, OpenAPI 3.0 compliant

LLM 的基础接口——chat, image, translate, transcription——与 OpenAI 接口兼容，其它接口可自定义 endpoint tag

### Candidates on model adapters

> 我们在评估的时候，要注意基本能力
>
> - 稳定性
> - rate limit. 必须得有能力控制渠道来源的 RPM/QPM 等，这是 model adapter 的基本素养
> - health check. 最好自身具备 health check 的能力，即便不行，要从外部 HA 查活。注意查活的代码要复用

接口提供商众多，显然我不会自己实现 model adaptors, 尽量减少再造轮子。可关注的项目有如下

| Project            | Pros                                                                                                 | Cons                                                          | 部署                                                  |
| ------------------ | ---------------------------------------------------------------------------------------------------- | ------------------------------------------------------------- | --------------------------------------------------- |
| LiteLLM            | 1. 设计目标完全匹配<br />2. 支持所有主流 providers<br />3. 具备基本的 cluster 部署设计<br />4. 用 ts 实现强类型                   | 1. 大部分 python, 启动很慢<br />2. 文档乱七八糟，proxy mode 的文档缺胳膊少腿而且前后矛盾  | seflhost, 依赖 pg, 不怎么吃资源，1G 内存跑得动                    |
| oneapi 系列          | 1. golang<br />2. 对接模型很多，发展很久，稳定                                                                     | 1. 面向接口转发商的项目，集成了无数的计费、日志、分发的功能，很难解耦<br />2. 都是国内作者，没有技术讨论的社区 |                                                     |
| ~~simple-one-api~~ | 1. golang<br />2. 作者的思想很清楚，就是简简单单的 api adapter, <br />而且关注免费资源的利用+国内大模型的对接<br />3. json 配置文件管理，无更多依赖 | 1. 就一个作者，佛系更新                                                 | docker 部署，or single binary, 简单。如果 cloudflare        |
| uni-api            | 1. 目前支持海外 API 为主<br />2. 同上，简洁明了风格                                                                   | 非常好，更新频繁，适合 gemini 这种三天两头调整接口但是文档一团糟的                         | 作者有意 cf workers, 无奈 workers 还不支持。现在支持了 vercel，但是不稳定 |

#### oneapi 变种

oneapi 系列变种繁多，oneapi, newapi, onehub, chatapi。。用了一圈下来，反倒觉得 one-api 原始项目最合适

- one-api 社区还是比较活跃的，更新也比较稳。比如难度最大的 vertex-ai，它的实现是最有生产环境考量的，因为是通过 Service Account+Location 实现，方便设置 claude3.5sonnet 这种只有西欧和美东两个地区上线的模型，不支持代理
- new-api 支持单个渠道设置代理，但是不支持 vertexai
- One-hub 虽然开发者的 QQ 群是最为活跃和友善的，但显然完全用爱发电，没有生产化交付的兴趣
- chat-api 的更新最频繁，虽然项目关注度不高，但是明显作者是行业内的，有动力更新，最近几个月(2024.5~8 月)模型更新后它这是最先上马的。vertex-ai 支持使用了 ADC
- 始皇的 new-api. 他提供 oai 和 anthropic 的商业反代服务，功能简单，支持 stripe

根据实测，以上所有系统的数据库结构都不兼容。以及，one-hub 似乎做了最多的 pg 数据库的兼容代码，实测下来，不关注计费、账户管理、调用日志的话，其实 pg 都能用。


#### simple-one-api

~~开发群也是 QQ，作者挺友善~~ 
太佛系了

[配置说明](https://rsxlecz746.feishu.cn/wiki/PkFywGAcyiLgkJkWbcscsBpPn6e)

[模型介绍](https://rsxlecz746.feishu.cn/wiki/Ljh3wocU8i21X5kEXeicom2Rnbr)

#### Portkey

> there is currently a difference in the open source v/s hosted version of Portkey. The hosted version is available on https://app.portkey.ai/ whereas the open source version can be installed by running npx @portkey-ai/gateway.
The open source version is completely headless - it does not come with any sort of UI. It only spins up a Portkey Gateway server on your local system with which you can route your requests and apply fallback, loadbalancing and other mechanisms using the Configs.
Whereas the hosted version comes in built with observability, and the full dashboard where you can see each request, it's cost/token details, and more.
Caching and virtual keys are two features that are not supported on the open source version, but available on the hosted version.
(this is because we didn’t want to add any redis / vector db / etc setup complexities to the open source version)

妈的就是不能把话说清楚，根本没有文档提到这一点，反倒是强调说他们生产环境用的是同一套代码 that serves billions of request per month
把 key 放到本地倒不是什么大问题，但是没有 caching 的话我要他干嘛？

#### 逆向

- https://github.com/lanqian528/chat2api
- https://github.com/maxduke/go-chatgpt-api
- https://github.com/aurora-develop/Duck2api
- https://github.com/Harry-zklcdc/go-proxy-bingai
- https://github.com/xqdoo00o/ChatGPT-to-API
- LLM Red Team 的几个作品, 用于逆向国内模型。可以偶尔测试、show 下，但不要浪费时间做任何实际工作

目前使用的只有 lanqian 的 chat2api 和 aurora 的 d2a. 

c2a 的使用技巧
- 多 rt 堆轮询
- UA 不要用内置的，一定要改
- IP 干净是大前提
- 多内置几个ua然后用算rt hash的方式 几个rt固定一个ua 在oai眼里就是多个真实用户 很稳

![](https://docsbed.lvtu.in/2024/10/24/17297377394317.jpg)


### mini apis

也可灵活使用 cloudflare workers/ aws api gateway/ google cloud run 等稳定的 serverless frameworks(关键免费)实现 mini apis 随用随取

比如[这个](https://medium.com/google-cloud/building-apis-with-cloud-functions-and-api-gateway-3892e1301cb4) 当然，考虑到 cf 流量无限但运算能力有限、AWS/GCP 的 free tier 都有限流量但是运算宽敞的多，可以结合食用



## Development

- API 调试和开发工具
  - Bruno. 记录 requests, 即时保存到 `api-playground` repo, 手动定期同步到 github
  - 如果需要观测三方 App 的 API, 用国产软件 Reqable, 完美取代 Charles. 
  - collection 可以导入 Postman 进行后续设计
- Gateway 开发
  - VSCode
  - Pycharm
  - terraform



## Deployment

### Selfhosted Model Adapters

现在感觉最大的问题是 rate limit, 然后是接口的实现稳定性。虽然套壳方便，聚合也轻松，但是实际上各家多一层就总是会引入 bug，并不是直接转发请求那么简单。从 stream/nonstream 的处理，到 finish_reasion, 到特定的消息格式(user/system/assistant message 序列的处理)，到 chat completions 的 parameters，etc...上游服务商也是在不断调整中，套壳就得持续跟进，引发的工作量还是很大的。

初步完成了 nxapi CF worker 后，经过一段时间的运行，现在调整策略为：

1. 套壳还是需要，重点职能从过去的归一化 OAI compatible endpoint 变为
   - 仅仅关注少数重点模型的服务商，以 class >=3 为主。class 是我后来引入的一个 provider def parameter, >=3 表示official/widespread model provider，比如 LlaMa 系列模型，`meta.ai` 是 official provider, `groq` 是 widespread 
   - 调度，也就是多 crendential 的调度，实现单一 endpoint 上的更高并发
2. 尽量使用 provider 的官方 SDK。也就是不手搓 message protocol adapter 了，之前 vertexai 的实现幸好用了 cursor 和别人的代码，没花特别多功夫，现在要改为直接用 Google 提供的 lib. 
3. 场景侧重：日常使用>开发>RAG，因为后者要么用 cursor/zed 自带模型能力, 要么 RAG 其实自己的 ops 逻辑就要实现 lb/error handling，而且现在我们的 RAG 技术路径未知，大概率手搓和 SaaS 都得用



#### OneAPI 系列

使用 serverless database + redis 在不使用本地存储的情况下同步 channel/tokens/site 数据，disable 计费和使用日志后，实测发现并没有什么实时运行的日志记录，数据库读写只会发生在资源修改和定期同步 channel 信息的那一刻。

- 只建一个用户，admin 

- 数据库

  1. 使用 serverless neon

  - 标准的 postgres 连接

  - multiple database + multiple roles: 可以设置多个 database, 应对 oneapi/chatapi/onehub database schema 不同的情况

  - branching: 每个 database 的 main branch 在某个区域的 0 号 host 配置，新建 branch 对应不同区域的配置，因为可能使用不同的接入点，或有的 host 需要新增代理等

  - 如果 channel 有更新，两个方法同步
    - branch pull from main. 然后手动进入后台更新。适合更新较为简单的情况
    - 记录每个 branch 的 created time, 之后的所有更新都通过 oneapi API 完成。需要自行开发

  2. aiven mysql(current)

  - advanced configurations.  设置 sql_require_primary_key = Disabled
  - create user, create database 
  - Database URL 去除最后的 sslmode

##### OneAPI API

抓请求分析倒是不难，发现很恼火的问题，GET channel 信息得到的 json 不包含 key; onehub 请求 channel 列表不包含 key, 请求单个 channel 可以返回 key 

options 相关的 API 就没必要了，直接修改 database table 吧

token 也可以直接修改数据库



其实这么看来，还是修改数据库最简单，同一个系列的可以保证兼容性，至于 neon 的 feature， 在 aiven 上可以用

- 多个 database
- 手动用程序和 api requests 取代



#### ~~simple-one-api~~

哥们已经很久不更新了，看来真的是做着玩的。不能跟了。



#### CF Workers AI

只能说作为备用，免费配额都是些方便免费本地部署的模型



#### LiteLLM 

资源需求较高，但是 for LLM developers

#### Local LLM Providers

- Ollama。现在也支持并发了
- vLLM  应该是公认性能最好的，支持并发，支持只使用 CPU, 需要自行 docker build 部署。host 内存 16G+
- sglang 号称比 vLLM 更快，[参见](https://lmsys.org/blog/2024-07-25-sglang-llama3/)。
- [Xinference](https://github.com/xorbitsai/inference)  ([Docs](https://inference.readthedocs.io/zh-cn/latest/getting_started/using_docker_image.html)) 支持多种推理后台(以上几个). 有便于管理的 webui, 支持集群，支持使用 cpu，但是 apple silicon GPU 支持有限。Xinference会根据硬件平台和模型特点，自动选择最佳的推理路径和算子优化策略，提高模型推理速度和性能。
  最快速的体验方式是 collab; 最廉价可靠的方式是 AutoDL. 
  example: [xf + LLamaIndex](https://docs.llamaindex.ai/en/stable/examples/llm/xinference_local_deployment/) 

References 
- 这篇中文教程讲的比较详细，但是仍然推荐 docker 部署  https://segmentfault.com/a/1190000045179096
