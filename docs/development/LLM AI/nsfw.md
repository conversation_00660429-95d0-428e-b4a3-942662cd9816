---
sidebar_position: 5
label: NSFW
---

## What is NSFW
> On Reddit, "NSFW" stands for "Not Safe for Work." It is used to label content that may be inappropriate to view in public or professional settings, such as sexually explicit material, violence, or other content that could be considered offensive or graphic. When a post or subreddit is tagged as NSFW, Reddit applies filters and warns users before they view the content, allowing them to decide whether or not they want to proceed.

> NSFW content is common on various online platforms to help users manage potentially sensitive content in appropriate settings.

如果从现阶段 AI 产品运营的角度出发，我们考虑划入如下内容
- ERP. Erotic Role Play. 如软色情角色扮演
- Porn. 单纯的 pornographic content, 图文到视频。
violence 必须避免

注意，世界各地的监管政策不同，但也存在共通的伦理(ethical)原则，如禁止 child abuse、hatred violence 等

通俗的说法也可能是 `uncensored`、 `unfiltered`、`擦边`、`黄色` 等

## What's the market like
作为合理的人类原始欲望的代表，色情产业总是最受瞩目也发展最快的。大语言模型落地至今，跑出来的产品模式不多，角色扮演在 c.ai 的代表下一度被认为是 ChatGPT 之后的下一个爆发点，但实际上增长乏力，也不存在商业模式。倒是阳光之下的色情领域的角色扮演，如二次元、软色情、泡妞等增长和收益双丰收。

更可观的是，全世界都是如此，哪怕中东这种伊斯兰教控制下的压抑国度。

传播渠道，在上一代移动互联网沉淀下来的语聊、黄播基础上，以 tg/discord 这种监管宽松的新形态 IM/Social 平台上，此类应用得到了飞速传播。

## What are everyone doing now
- (LLM powered)deepfake，如脱衣，换脸等
- ERP
- 情趣用品的软硬件整合


## Tech Sheet
重点来了，如何实现。理论上所有的 foundation model 都可以用用于 uncensored product 的开发，但闭源模型如 OAI/ChatGLM 等由于监管严格，并不适合此类场景，其它模型也由于训练特点的不同，`get creative` 或 jailbreak 的扩展能力不尽相同，所以 NSFW 产品的开发实际上也适用于任意 `creative writing` 的场景。


### Architecture
```mermaid
graph TD

%% User Interaction
User -- Sends Message --> Telegram/Discord

%% Platforms
subgraph Messaging Platforms
    Telegram/Discord -- API Calls --> BotIntegration
end

%% Bot Integration Layer
BotIntegration -- Processes Message --> OrchestrationLayer

%% Orchestration Layer
subgraph Orchestration Layer
    OrchestrationLayer -- Text Input --> LanguageModelAPI
    OrchestrationLayer -- Image Prompt --> TextToImageAPI
    OrchestrationLayer -- Speech Request --> TextToSpeechAPI
    OrchestrationLayer -- Content Check --> ContentModerationService
    OrchestrationLayer -- Data Query --> KnowledgeBase
    OrchestrationLayer -- Store/Retrieve --> Database
end

%% Backend Services
subgraph Backend Services
    LanguageModelAPI[Language Model API (LLava)]
    TextToImageAPI[Text-to-Image API (Stable Diffusion)]
    TextToSpeechAPI[Text-to-Speech API]
    ContentModerationService[Content Moderation Service]
    KnowledgeBase[Knowledge Base (RAG)]
    Database[(Database)]
end

%% Responses
LanguageModelAPI -- Text Response --> OrchestrationLayer
TextToImageAPI -- Generated Image --> OrchestrationLayer
TextToSpeechAPI -- Audio File --> OrchestrationLayer
ContentModerationService -- Compliance Status --> OrchestrationLayer
KnowledgeBase -- Retrieved Data --> OrchestrationLayer

%% Orchestration to Platforms
OrchestrationLayer -- Message/Image/Audio --> BotIntegration
BotIntegration -- Sends Response --> Telegram/Discord
Telegram/Discord -- Delivers to --> User

%% Internal Development Frontend
InternalFrontend[Internal Frontend (Dev/Test)] -- Test Inputs/Outputs --> OrchestrationLayer

%% Infrastructure
subgraph Infrastructure
    Authentication[Authentication & Authorization Module]
    LoadBalancer[Load Balancer]
end

%% Connections
BotIntegration -- Authenticates --> Authentication
OrchestrationLayer -- Handles Scaling --> LoadBalancer

%% Notes
Note over InternalFrontend,OrchestrationLayer: Frontend is used only for internal development and testing purposes.

```

- User Interaction:
    Users interact with your service through Telegram or Discord by sending messages.
    The messaging platforms communicate with your system via bot integrations.

- Bot Integration Layer:

Acts as an intermediary between the messaging platforms and your backend.
Processes incoming messages and forwards them to the Orchestration Layer.

- Orchestration Layer:

    The core component that manages the workflow.

Interacts with various backend services:

    - LanguageModelAPI (LLava): Processes text inputs and generates responses.
    - TextToImageAPI (Stable Diffusion): Creates images based on text prompts.
    - TextToSpeechAPI: Converts text responses into audio.
    - ContentModerationService: Checks all content for compliance.
    - KnowledgeBase (RAG): Retrieves additional information to enhance responses.
    - Database: Stores user sessions, preferences, and history.

- Backend Services:

    Provide specialized functionalities as required by the Orchestration Layer.

Responses:
    Processed data from backend services are returned to the Orchestration Layer.
    The Orchestration Layer compiles the final response and sends it back through the Bot Integration Layer to the user.

Infrastructure:
    Authentication & Authorization Module: Ensures secure access and verifies user credentials.
    Load Balancer: Manages network traffic to maintain performance and scalability.

Internal Development Frontend:

    Used exclusively for development and testing.
    Interacts directly with the Orchestration Layer to simulate user interactions and test system responses.



### Foundation Models+Dev
选择合适的基座模型，以及进行 LoRA 微调或者 FT 是必经之路。

- [oobabooga](https://github.com/oobabooga/text-generation-webui)  [wiki](https://github.com/oobabooga/text-generation-webui/wiki)
- [KoboldAI](https://github.com/KoboldAI/KoboldAI-Client)  使用 gguf model 的 [KoboldCpp](https://github.com/LostRuins/koboldcpp)
- [[Megathread] - Best Models/API discussion](https://www.reddit.com/r/SillyTavernAI/comments/1g39qjg/megathread_best_modelsapi_discussion_week_of/)


- [AI Horde](https://github.com/Haidra-Org/AI-Horde) 看起来是提供计算资源的？

### Frontend
- [SillyTavern](https://github.com/SillyTavern/SillyTavern) 传说中的小酒馆
- [KoboldAI TG Bot](https://github.com/YellowRoseCx/KoboldAI-Telegram-Bot)  自己开发没多难，tg bot 我认为最好还是跑到 serverless 上
- [Wyvern](https://github.com/WyvernChat/Wyvern) 很久没更新了


## References
### Community
- Reddit r/NSFW KobaldAI [r/oobabooga](https://www.reddit.com/r/Oobabooga/) 等
- Discord ST
- youtube 上有一些百无禁忌讲此类内容的
- tg 群不知道

想知道中文社区的人都在干嘛

### discord #ST 的一个老兄提了很多起步建议
ahh gotcha gotcha since running models locally implies running them on your computer so you'd need VRAM for that. an alternative would be using google colab but there's a time limit per google account, or you can use Horde or WyvernChat

colab offers 16gb iirc sooooo you can run 12b models,  this is a koboldcpp colab, runs KCPP as the backend. Did you manage to get ST running n stuff?

[Google Colab](https://colab.research.google.com/github/LostRuins/koboldcpp/blob/concedo/colab.ipynb)


These are the 2 models I'd recommend, pick whichever model whose name you prefer and paste the link in the model field on the colab thingie 

https://huggingface.co/mradermacher/MN-LooseCannon-12B-v2-GGUF/resolve/main/MN-LooseCannon-12B-v2.Q5_K_M.gguf

https://huggingface.co/mradermacher/Magnum-Picaro-0.7-v2-12b-GGUF/resolve/main/Magnum-Picaro-0.7-v2-12b.Q5_K_M.gguf

set context size to 8192 for starters as well

both models rely on chatml formatting so grab that file and import it into ST's advanced formatting tab

`ST-formatting-2024-10-20.json`

1. it's the big A icon at the top of the ST screen, `master import` button in the top right corner

----

yeye i *specialize* in testing models for creative writing (rp, adventure, storywriting, etc...) and all that and these models stood out the most to me 

THERE IS ANOTHER https://huggingface.co/bartowski/Gemma-2-Ataraxy-9B-GGUF/resolve/main/Gemma-2-Ataraxy-9B-Q5_K_M.gguf

this one is super different, the writing is really original but it MIGHT be a little more reserved when it comes to nsfw stuff



also one last thing, text completion preset, for settings

`High temp - Min P (7).json`

temperature is the wackiness slider and min p is the boring slider, good luck