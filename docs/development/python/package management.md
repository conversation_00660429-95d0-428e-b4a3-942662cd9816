
## TL;DR
- conda 太臃肿，适合多 runtime 的管理，包括 node, R, python etc. 抛弃
- pyenv 是轻量级的标准 venv 替代品，可用，但不是必须的
- pip 慢、低效、权限管理过时，系统必备但是不要用

**Dev** (linux or macOS)
- pipx
- uv
- (optional)micromamba

**Production(linux)**
- uv
- docker
- (optional)mise or micromamba 

## conda
也就是 Anaconda/Miniconda 

如果已经安装过，历史许多 project 已经靠 conda 管理 env, 又懒得给所有 proj 重建。那么可以保留 `conda` 命令但是不默认开启 `base` env. 需要在原 `.bashrc` / `.zshrc` 中注释掉原有 `conda init` 那部分脚本，添加

```bash
export PATH="/opt/miniconda3/bin:$PATH"
conda config --set auto_activate_base false
source "/opt/miniconda3/etc/profile.d/conda.sh"
```

### cleanup and migration
```
# remove an env entirely
conda env list
conda remove -n ENV_NAME --all -y

# list all packages in a conda env 
conda list -n ENV_NAME

# export all packages in an ENV installed via `conda install`, and WITHOUT dependencies
conda env export --from-history -n ENV_NAME

# export everything including dependencies and installations using pip
conda env export -n aps > environment.yml

```

## pipx
pipx 相当于 pnpm, 可以简单理解为
1. 安装 global command. 比如 ansible, posting, ipython 等
2. 临时安装、运行一个 python application. 
但是它不能：为系统 python 环境安装 packages, 比如你想在不启用 venv 的前提下写个简单的 Python 脚本，不是 pipx 做的事儿。这时还是需要 `pyenv`

### Installation
- macOS
`brew install pipx`
`pipx ensurepath`

- Linux
```
# Install Python and pipx via OS packages
sudo apt install python3 python3-pip python3-venv
python3 -m pip install --user pipx

# Install tools globally with isolation
pipx install httpie

```

 -  **Isolated Environments for Each Application:**
	 1. Pipx creates a dedicated virtual environment for each application you install. This isolates the application's dependencies from your global Python environment and from each other. This prevents dependency conflicts and ensures that each application runs with the specific versions of libraries it requires.
	 2. You don't have to worry about installing and managing dependencies globally
	 3. **Prevents System Python Pollution:** By isolating applications, pipx prevents them from interfering with your system's Python installation or other Python projects.
- Easy Installl/Uninstall/Upgrade. 
	- `pipx install <package>` 
	- `pipx uninstall <package`
	- `pipx list`
	- `pipx upgrade-all`
- **Convenient Access to Executables**
	- **Automatic PATH Management:** Pipx automatically adds the executables from the installed applications to your system's `PATH`. This means you can run the applications from your terminal just like any other command
	- **`pipx run` for Temporary Use:** If you don't want to permanently install an application, you can use `pipx run <package_name>`. perfect for one-off tasks

几个预设的环境信息
- `macOS` 上的 venv 保存在 `~/.local/pipx/venvs` 
- Pipx, by default, uses the **first Python executable it finds on your `PATH`**. It prioritizes the `python3` command if it exists, falling back to `python` if `python3` is not available.

## universal version manager - mise
[The frontend to your dev env](https://mise.jdx.dev/)
处理任何 [dev tools](https://mise.jdx.dev/dev-tools/)，不仅仅是版本管理，也包含 env variables。非常新的工具，比 pyenv 快(in rust)，更新很频繁，非常 promising 值得关注，请完整阅读文档，写的很清晰。

安装 dev env 的时候，会先尝试下载 prebuilt binaries, 如果没有则会自动 download and compile。实测在 arm64/x64 linux 和 macOS 都有 prebuilt, 但 alpine linux 没有。

honor `.python-version`, 使用 `mise.yaml` 管理项目

支持两种加载 dev tools 的方法，详细参看[文档](https://mise.jdx.dev/dev-tools/shims.html)
- `mise activate`  Recommended for Interactive Shells
	- Automatically updates environment and PATH when changing directories
	- Best for daily development work
	- Most feature-complete option
- `shims`   Good for Non-Interactive Sessions
	- Add mise's shims to PATH
	- Works well for CI/CD, IDEs, and scripts
	- Some features may not be available
最后，它也支持像 `uvx` 一样 **Direct Execution** (No Configuration Required)

- Use `mise exec` or `mise run` commands directly
- Run one-off commands without modifying shell environment
- Example: `mise exec -- node -v`

### installation
支持 alpine, 虽然也可以通过 `apk add` 安装，但是后续更新慢，还是用 script 安装

```
# 缺省安装到 ~/.local/bin
curl https://mise.run | sh
# or with options
curl https://mise.run | MISE_INSTALL_PATH=/usr/local/bin/mise sh

# self update
mise self-update
```
其实是个 single binary. 

**Activate** in your interactive shell, 类似的可用于 bash, fish 
```
echo 'eval "$(mise activate zsh)"' >> ~/.zshrc
```
for bash
```
echo 'eval "$(mise activate bash)"' >> ~/.bashrc
```

然后看 `mise doctor`

### configuration
配置文件在 ~/.config/mise/config.toml

```
# 这样之后，shell 启动就会使用这个版本，而且不会像 mm/conda 一样提示当前的 venv. 
mise use --global python@3.13

# 如果想要 revert back system python. 可能 mise unuse 也行？
mise unset -g python
mise deactivate

```

[IDE integration](https://mise.jdx.dev/ide-integration.html) 是个问题，挺麻烦。简单说 vscode 用 extension; nvim/XCode 也可行

由于很轻量，所以在 [CICD](https://mise.jdx.dev/continuous-integration.html) 中使用 mise 维护 env 是个不错的选择。Github Actions/Gitlab CI 都支持。

### 有必要结合 uv 食用吗？
[mise for python development cookbook](https://mise.jdx.dev/mise-cookbook/python.html)

what mise adds to the equation:

- **Unified Environment and Task Management**  
    Mise is built to streamline developer workflows not only for Python but also for other languages and tools. It provides a single configuration (via `mise.toml`) to manage tasks, version syncing, and environment settings consistently across projects.[2](https://mise.jdx.dev/lang/python.html)
    
- **Cross-Tool Integration**  
    Even if uv already creates your virtual environment, mise has features to detect uv-created environments and synchronize the Python version used in your project using commands like `mise sync python --uv`. This ensures consistency if your project requires alignment with other tools or environments.[1](https://mise.jdx.dev/mise-cookbook/python.html)
    
- **Task Automation and Script Management**  
    Mise offers an easy way to define and run project-specific tasks—like linting, testing, and running scripts—which can simplify development for projects that extend beyond basic dependency management.[1](https://mise.jdx.dev/mise-cookbook/python.html)

### pnpm/yarn 呢？
[类似的](https://mise.jdx.dev/mise-cookbook/nodejs.html)
简单说， uv/yarn/pnpm 都可以在 mise 里安装，per project 的选择。如果要使用外部独立安装的这几个工具也(应该)没问题。

## version manager - pyenv
轻量以及广被宣传的做法, 使用 shims 管理 environments.

> [!NOTE]
> 不支持 alpine!


pyenv 安装 Python 运行环境的做法是 download and compile from source. 这自然需要系统安装 dev libraries, 而且需要更多时间，它不支持从 conda-forge 下载 prebuilt. 参看 [Suggested build environment from pyenv](https://github.com/pyenv/pyenv/wiki#suggested-build-environment)

简单说
- for macOS  `brew install openssl readline sqlite3 xz zlib tcl-tk@8`
- for Ubuntu/Debian Linux    
```
sudo apt update; sudo apt install build-essential libssl-dev zlib1g-dev \
libbz2-dev libreadline-dev libsqlite3-dev curl git \
libncursesw5-dev xz-utils tk-dev libxml2-dev libxmlsec1-dev libffi-dev liblzma-dev
```


## version manager - micromamba
推荐 [micromamba](https://mamba.readthedocs.io/en/latest/installation/micromamba-installation.html)，single binary 15M, 来自 conda-forge, 提供 prebuilt binary releases, 不像 pyenv 还得自己编译。

### installation
- Linux
```bash
"${SHELL}" <(curl -L micro.mamba.pm/install.sh)

# 然后按照提示配置 shell rc, OR
micromamba shell init -s <your shell> -p ~/micromamba
```
- macOS
`brew install micromamba`
`micromamba shell init -s zsh -p ~/micromamba`

### configurations
- 查看当前 config  `mm info`
#### channels
缺省使用的 channel 是来自 anaconda 的 commercial channels , 如
```
                         https://repo.anaconda.com/pkgs/main/noarch
                          https://repo.anaconda.com/pkgs/main/osx-arm64
                          https://repo.anaconda.com/pkgs/r/noarch
                          https://repo.anaconda.com/pkgs/r/osx-arm64
```

但我们需要切换到 `conda-forge` 这个 opensource 的, 完全删除 default channel 可以的
```
micromamba config append channels conda-forge
micromamba config remove channels defaults
```

如果要增加其它特定用途的 channels
```
micromamba config prepend channels bioconda 
# Ensure conda-forge is higher priority
micromamba config prepend channels conda-forge 
```

- base env
与 conda 显式创建 base env 不同的是，mm 缺省创建 base env，但是没有 python 环境，需要手动安装。以及这个 base env 并不是出现在 `envs` 的一个项目，而是不指定 mm env 的时候就会缺省使用的环境。缺省的 python exec 是 `~/mamba/bin/python` 

给 base 安装 py 环境
```
micromamba install -n base python=3.13 -c conda-forge
# OR rollback
micromamba install -n base python=3.13
```

- enable/disable default env
```
micromamba config set auto_activate_base false
```
 
### core commands cheatsheet
```shell
micromamba self-update

# parallel installs
micromamba install -y --jobs 4 numpy

# specify `-c conda-forge` for optimized binaries
-c conda-forge

# default install to base env
# If alraedy activated in another env, then micromamba install would install to the current env 
micromamba install httpx pydantic

```

#### useful aliases
```
alias mm='micromamba'
alias mmi='micromamba install'
alias mma='micromamba activate'
alias mmd='micromamba deactivate'
```

#### **Environment Management**

|Command|Action|
|---|---|
|`micromamba create -n myenv python=3.12`|Create env with Python 3.12|
|`micromamba activate myenv`|Activate environment|
|`micromamba deactivate`|Exit current environment|
|`micromamba env list`|List all environments|
|`micromamba remove -n myenv --all`|Delete environment|

#### **Package Operations**

|Command|Action|
|---|---|
|`micromamba install -n myenv numpy pandas`|Install packages|
|`micromamba update --all`|Update all packages|
|`micromamba list`|Show installed packages|
|`micromamba search tensorflow`|Search packages|
|`micromamba remove numpy`|Uninstall package|

#### **Configuration**

|Command|Action|
|---|---|
|`micromamba config prepend channels conda-forge`|Add channel priority|
|`micromamba config set auto_activate_base false`|Disable auto-base activation|
|`micromamba config list`|Show config settings|

### Advanced workflows
- create environment yaml
```yaml
# env.yml
name: data-science
channels:
  - conda-forge
dependencies:
  - python=3.11
  - numpy
  - jupyterlab

```
Run:
`micromamba create -f env.yml

- **Cross-Platform Binary Management**
```
# Linux: Pre-compiled binaries
micromamba install -c conda-forge --platform linux-64 numpy

# macOS: ARM optimizations
micromamba install -c conda-forge --platform osx-arm64 tensorflow

```

## uv
The King and the future, for python development. 请仔细阅读[文档](https://docs.astral.sh/uv/)

### Installation
- Linux & macOS
```
# Install uv globally 
curl -LsSf https://astral.sh/uv/install.sh | sh 
source ~/.bashrc  # Or re-login # Verify installation 
uv --version`
```
如果是 macOS 也可以
```
brew install uv
```

### Migrating from conda to uv

2. 如果原来的项目的 dependencies 已经用 pip 的 requirements.txt 管理
```
cd your_project
uv init  # Creates .python-version, requirements.in, .gitignore
uv venv  # 缺省创建在 ./.venv
# Optional
source .venv/bin/activate
# Synchronize environment to exact specification. 不带 dependencies
uv pip sync requirements.txt
# Adds new packages/Upgrades existing packages if newer versions are specified
uv pip install -r requirements.txt
```

3. 如果原来的项目 dependencies 不完全管理，需要完整从 conda env 记录下来
由于 conda 的 `requirements.txt` 标准跟 pip 的 PEP440 规范不同，麻烦，算了。

### managing python envs
uv 自带[管理 python 环境](https://docs.astral.sh/uv/concepts/python-versions/#pypy-distributions)的能力，使用 prebuilt cython binaries, 支持 CPython, PyPy, and GraalPy Python implementations. 

使用 `uv init` 创建 project 的时候会选择最低版本的 python executable 作为 `python-version`

如果当前 project 的 python-version 不匹配，by default, uv will automatically download Python versions when needed.

```
uv python list
uv python install 3.12 3.11
uv python uninstall 3.12

# 使用特定的 distribution
uv python install pypy
```

几点说明
- as of 2025.1, alpine 上不提供 python 此时还是需要用 pyenv 或者 mise 

### 关于 pip
`uv pip` 只是提供 drop in replacement for traditional users, but still tens of times faster. 

使用 `uv pip` 时首先需要关注[创建和使用 python env ](https://docs.astral.sh/uv/pip/environments/#discovery-of-python-environments)的方法 
uv will search for a virtual environment in the following order:

- An activated virtual environment based on the `VIRTUAL_ENV` environment variable.
- An activated Conda environment based on the `CONDA_PREFIX` environment variable.
- A virtual environment at `.venv` in the current directory, or in the nearest parent directory.

If no virtual environment is found, uv will prompt the user to create one in the current directory via `uv venv`.



### ** Workflows**  
1. **Development**  
目前 python 还不支持在 pyproject.toml 定义 `dev-dependencies` 但是快了。现在通过 uv 可以实现 [manage dev dependencies](https://docs.astral.sh/uv/concepts/projects/dependencies/#development-dependencies)
development dependencies are local-only and will _not_ be included in the project requirements when published to PyPI or other indexes. As such, development dependencies are not included in the `[project]` table.
```
uv add --dev pytest
```
`pyproject.toml`
```
[dependency-groups]
dev = [
  "pytest >=8.1.1,<9"
]
```

```bash
# 在 project level 配置，最好用 uv add/remove
uv add httpx
# 指定特定来源和版本
uv add "httpx @ git+https://github.com/encode/httpx"

# 如果之前的项目使用了 requirements.txt
uv add -r requirements.txt

# Add new package to existing environment
echo "requests==2.32.0" >> requirements.txt
uv pip install -r requirements.txt


```

1. managing packages inside a project
```bash
uv add httpx
```


3. **switch python versions later**
```
# Pin new version, 
uv python pin 3.12  # or any other version

`# UV will automatically: # 1. Update .python-version file # 2. Remove old virtual environment # 3. Create new one with specified version`
```
4. **replace `pipx`**
```
# install globally
uv tooll install httpie
# oneoff 
uvx httpie

# Upgrade installed tools 
uv tool upgrade --all 

# List installed tools 
uv tool list

# 指定 python 版本
uv tool install posting --python 3.12 --prerelease=allow --reinstall
```

### work with Jupyter
参见[文档](https://docs.astral.sh/uv/guides/integration/jupyter/#using-jupyter-within-a-project)，一定得有个 kernel 
```
uv add --dev ipykernel
```

## Task Runners
This section covers tools for running tasks in a Python project, such as testing, linting, and building.

### poethepoet
> [!NOTE]
> This is the current selection for our projects.

[poethepoet](https://github.com/nat-n/poethepoet) is a task runner that works well with `uv` and `poetry`. It allows you to define tasks in your `pyproject.toml` file. Tasks can be simple commands, shell scripts, or Python functions.

**Key Features:**
- **Simple Declaration:** Tasks are defined in a `[tool.poe.tasks]` section in `pyproject.toml`.
- **Virtual Environment Integration:** Automatically runs tasks in the project's virtual environment.
- **Argument Passing:** Supports passing additional arguments to tasks from the command line.
- **Cross-platform:** Works on Linux, macOS, and Windows.

**Example `pyproject.toml` configuration:**
```toml
[tool.poe.tasks]
test = "pytest --cov=my_app"
serve.script = "my_app.service:run(debug=True)"
```

### hatch
Hatch is an extensible Python project manager. It provides dependency management, virtual environment management, and a flexible task runner system through plugins.

**Key Features:**
- **Project Scaffolding:** `hatch new` creates a new project with a standard layout.
- **Environment Management:** `hatch env` manages different environments for your project.
- **Task Runner:** Define and run tasks using `hatch run`.
- **Extensible:** Supports plugins for adding new functionality.


## poetry
先把 uv 用顺溜，暂时不用考虑 poetry 了。

但如果需要二次开发 poetry 项目，可以参看这个 [migrate from pyenv+poety to uv](https://data-ai.theodo.com/en/technical-blog/migrate-to-astral-uv)  简单引用

5. Go to your project folder
6. Delete the `.python-version` file if it exists;
7. Run `uvx pdm import pyproject.toml` (`uvx` is used to install command line tools, in the same way as `pipx`);
8. Update the new `pyproject.toml` by deleting `poetry`-related sections and renaming `pdm`-related sections (see the [uv docs](https://docs.astral.sh/uv/reference/settings/) for `pyproject.toml` format);
9. Generate the `.venv` folder by running `uv sync`.

As you see, it’s very easy and shouldn’t take more than 5 minutes.

To update all code files (Makefile, CI/CD, Dockerfile, ...) which use `poetry` and/or `pyenv` commands, you can use the workflow Gist above or the uv docs to translate old commands to their uv version, which should also be very easy. For Dockerfiles, Astral provides a doc page of good practices [here](https://docs.astral.sh/uv/guides/integration/docker/) that are worth reading.