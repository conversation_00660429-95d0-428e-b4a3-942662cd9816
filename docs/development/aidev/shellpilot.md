# shell pilot(lumensh)

Lu<PERSON>, shell 拼接而成

1. Hermes (赫尔墨斯): 希腊神话中的信使之神，象征着沟通和智慧。

Lynx: 猞猁，一种敏捷、机智的野猫，象征着敏锐和智能。

Aether (以太): 希腊神话中的上层空气，象征着智慧和连通性。

Nox: 拉丁语中的"夜晚"，暗示着在终端中工作的夜猫子程序员。

Thoth: 埃及神话中的智慧之神，也与书写和魔法相关。

Corvus: 拉丁语中的"乌鸦"，象征着智慧和适应性。

7. Sylph: 空气元素精灵，象征着轻盈和智能。

Nexus: 拉丁语中的"连接"或"纽带"，暗示着您的工具连接人类和AI。

Lumen: 拉丁语中的"光"，象征着启发和智慧。

Zephyr: 希腊神话中的西风之神，象征着轻快和效率。



## dev

- viper
- cobra
- github.com/tmc/langchaingo/llms
- github.com/ollama/ollama
- tcell 
- github.com/atotto/clipboard
- github.com/xo/terminfo



## existing solutions


| Name | Type | Programming Language | Interactive Mode | Key Features | Limitations |
|------|------|----------------------|------------------|--------------|-------------|
| [Warp AI](https://www.warp.dev/warp-ai) | Integrated terminal | Unknown | Yes | 直接在 terminal 里用 NLP | 免费用户每月 100 次调用，Team 用户 $18/p/m, 1000 次 Warp AI |
| [Termius](https://termius.com/) | Integrated terminal | Unknown | Unknown | 同上 | 订阅用户无限用吧，但只是辅助 shell command  |
| [Shell-AI](https://github.com/ibigio/shell-ai) | Terminal command | Go | No | YAML 配置，命令建议 | 最近更新 24.1 月，有点糙的。`q create a new git branch` 回车，然后给出指令，再回车，命令就被复制到 clipboard, 然后 ^V 执行 |
| [sgpt](https://github.com/tbckr/sgpt) | Terminal command | Go | Yes (via `rlwrap`) | 支持 vision model，更像是在 shell 里使用 gpt 的定位 | 显著更加完善、复杂，提供了一堆有得没的安装指南 |
| [shell-gpt](https://github.com/TheR1D/shell_gpt) | Terminal command | Python | Yes | 设计成在 shell 里跟用命令跟 oai models 对话，辅助工作、聊天等 | 通过 shellgpt[litellm] 支持调用本地模型——并不 work |
| [terminalGPT](https://github.com/adamyodinsky/TerminalGPT) | Terminal UI | Python | Yes | Terminal 里起一个本地 session 跟 openai 的 Model 对话 | 已经是过时的思维方式了 |

以上所有开源项目都已经很久不真正更新了。

### shell-gpt
mode 挺全面的其实 ，详见 [README](https://github.com/TheR1D/shell_gpt/blob/main/README.md)

```sgpt --shell --execute "make all files in current directory read-only"```

![executing the Linux shell command generated by ChatGPT ](https://beebom.com/wp-content/uploads/2023/03/sgpt_shell_execute.png?w=640)

### shell-ai
`http://localhost:11434/v1/chat/completions`



### sgpt
设置环境变量

```
$ sgpt -m "gpt-4-vision-preview" -i "https://upload.wikimedia.org/wikipedia/en/c/cb/Marvin_%28HHGG%29.jpg" "what can you see on the picture?"
The image shows a figure resembling a robot with a humanoid form. It has a
$ sgpt -m "gpt-4-vision-preview" -i pkg/fs/testdata/marvin.jpg "what can you see on the picture?"
The image shows a figure resembling a robot with a sleek, metallic surface. It
```

```
$ sgpt "mass of sun"
The mass of the sun is approximately 1.989 x 10^30 kilograms.
```

```
$ sgpt sh --execute "make all files in current directory read only"
chmod -R 444 *
Do you want to execute this command? (Y/n) y
```



## 我的简单思路

### 定位
- 开发者会被这批 IDE 革命影响成用 chat 解决开发问题；
- 小白用户(windows powershell, macos terminals, linux terminals)现在并不具备 daily 使用 terminal 完成工作的能力；

所以我觉得定位就是 seasoned 开发者之外的那批人

- 小白，势必可以用 terminal 完成更多的工作，比如 windows 用户看自己的 IP，激活 WSL ， clear dns cache 等常见操作
- 半瓶子水，有点技术基础的 rookie
- 不同偏好的初级、中级开发者

### Features

1. Basic Shell Copilot (Level 1)
   - Natural language command translation for system administration tasks
   - Assist with simple computer operations (e.g., file management, network configuration)
   - Help create and explain basic shell scripts and Apple scripts
   - Cross-platform support (Windows PowerShell, macOS Terminal, Linux shells)
   - Interactive learning mode with step-by-step explanations

2. Enhanced Terminal Assistant (Level 2)
   - Casual chat functionality within the terminal
   - Image upload and analysis capabilities
   - Multi-turn conversations for complex problem-solving
   - Integration with popular developer tools (git, npm, docker, etc.)
   - Context-aware suggestions based on current directory and recent commands

3. Advanced Integrated Terminal UI (Level 3)
   - Multi-pane interface similar to lazygit
   - Conversation management system for multiple ongoing chats
   - Resource management (e.g., files, processes, network connections)
   - Visual representation of system status and resource usage
   - Command history and favorites (similar to Warp Drive functionality)

Additional Features Across Levels:
- Progressive complexity introduction
- Customizable shortcuts and aliases
- Safety warnings for potentially dangerous operations
- Task automation for repetitive processes
- GUI-to-CLI translation to help users transition from graphical interfaces

This tiered approach allows users to start with basic assistance and gradually explore more advanced features as they become comfortable with the command-line interface. The design caters to a wide range of users, from beginners to more experienced developers, providing a pathway for growth and increased productivity.

1. 基础Shell助手（第1级）
   - 系统管理任务的自然语言命令转换
   - 协助简单的计算机操作（如文件管理、网络配置）
   - 帮助创建和解释基本的shell脚本和Apple脚本
   - 跨平台支持（Windows PowerShell、macOS Terminal、Linux shells）
   - 交互式学习模式，提供逐步解释

2. 增强型终端助手（第2级）
   - 终端内的日常聊天功能
   - 图片上传和分析能力
   - 复杂问题解决的多轮对话
   - 与流行开发工具集成（git、npm、docker等）
   - 基于当前目录和最近命令的上下文感知建议

3. 高级集成终端UI（第3级）
   - 类似lazygit的多窗格界面
   - 多个进行中对话的会话管理系统
   - 资源管理（如文件、进程、网络连接）
   - 系统状态和资源使用的可视化表示
   - 命令历史和收藏夹（类似Warp Drive功能）

跨级别的附加功能：
- 渐进式复杂度引入
- 可自定义的快捷键和别名
- 对潜在危险操作的安全警告
- 重复性过程的任务自动化
- GUI到CLI的转换，帮助用户从图形界面过渡

这种分层方法允许用户从基本辅助开始，随着他们逐渐适应命令行界面，探索更高级的功能。这个设计适合广泛的用户群，从初学者到更有经验的开发者，提供了一条成长和提高生产力的路径。

### Design Philosophy
- 用户不要关心用什么模型，完全可以类似 cursor-small, phind-codegen 一样，微调一个自己的模型
- 结合 prompt caching, 速度、运营成本都可以极大节省
- semantic caching 不知道怎么做，真有这个 ROI 的话可以顺便研究
- 多环境+多 shell 支持，windows, macos, linux
- 一定是有场景的，最新常用的 sys admin software, git, terraform, docker, applescript, 等等等。
- 不考虑特定 terminal software 的集成，不挣钱

### Opensource
go 编写，只支持 ollama, shell 命令指导

### Commercial SaaS
订阅制，不支持 bring your own model, 没啥 user dashboard, 就是像 supermaven 一样即开即用

- Free: 20次/mon 调用，输入一个 email 即开即用，不用注册、验证
- Hobby: $3/mon, L1+L2, 1000 次调用, 模型不可以选择，怎么快怎么来——当然实际上我 llm router 还是要开发的
- Pro: $5/mon, 支持 L3，有 user storage 的

交互模式还不知道怎么做到最好，跟 warp 似的有没有可能

**其实关键还是，以这个产品定位，价格不宜太高，首先肯定不能 >5， 所以不挣什么钱，time to launch 不要太费周章**

### 主要工作量

- 探索确定最 seemless 的交互方式，both local and remote
- golang 的 terminal UI 开发
- backend. 不用想一定是 cloudflare 为主. monthly user >500 后我会多付费一些产品，用于优化响应速度；>1000 后我会用 hf(or the like)部署模型
- user storage & profile(simple alternative to warp drive)
- FT 模型的话，再说吧。

### 展望
如果真做了，其实可以有衍生品，比如 chrome extension, macos shortcut, windows xxx(不熟悉), alfred workflow 什么的

