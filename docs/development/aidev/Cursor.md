---
sidebar_position: 2
---


## Community
- [论坛](https://forum.cursor.com/c/general/4)
- linux.do 很多人讨论。tg 上的群讨论水平比较低

## 白嫖
-  [fake-cursor](https://github.com/Angels-Ray/fake-rosrus) 以 cursor 插件形式工作，很方便，minimum changes. 
- [cursor-auto-free](https://cursor-auto-free-doc.vercel.app/zh/). 用 cf email worker + tempmail.plus 自动注册 pro trial 账户，并且 patch machind ID. 实现无限白嫖。但需要 patch cursor 本身，还影响了 keystore, 我懒得研究为啥
- cursor-api. wisdgod/cursor-api 感觉用起来太麻烦了，而且 repo 已经 archive 了，不知道最新的镜像在哪儿，而且最近发现 cursor 的 system prompt 也强制只回答开发相关问题了。
- [cursor pool](https://linux.do/t/topic/433091). L 专属，就是他们惯用的一群人贡献 pro trial 甚至黑卡 Pro 的账户，大家白嫖上车，过期或者次数到顶就自由切换一个 token. actually it pretty works, 省去了自己注册账户、patch machine ID 的麻烦。
- [cursor-shadow-patch](https://github.com/zetaloop/cursor-shadow-patch). 这个改动的很干净。[L 站原贴](https://linux.do/t/topic/486399/1)
- [cursor-auto-register](https://github.com/ddCat-main/cursor-auto-register)  一个基于 Python 的项目，运行简单，但还是依赖 Chrome 最为缺省浏览器，也挑 IP。我还没运行成功，因为 cf email verification 今天过不去。
- [cursor-free-vip](https://github.com/yeongpin/cursor-free-vip) 目前(0.48.x)更新的白嫖工具

等这些歪招都失败了，就老老实实付费吧

### 最新简易白嫖流程
需要
- 自己的域名
- 域名托管在 CloudFlare
- 懂得如何新建 subdomain 邮箱

白嫖大法简单说
1. 注册新账号。可以手动注册，也可以使用 auto-register 项目自动化完成，并且提取 token
2. 更新机器码(device ID or Machine ID)。用 curs0r 这个论坛的工具
3. 写入新 token。也用 curs0r

```
如果你是手动注册的话，不用机器人也行，登录网页的状态，curs0r里面点一下一键换号，随便输入点东西，然后直接cursor里点登录。

这样的话相当于 token 也不用提取了
```


## Getting Started
> 已经挪到了 `neuxs-docs`