---
sidebar_position: 2
---


> **⚠️ 本页内容已过时 / Obsolete**
>
> 本节涉及的社区讨论与“白嫖”相关方法已不再适用或维护，相关工具、流程和讨论均可能失效或不再推荐。请勿参考本页内容，建议直接考虑官方付费方案或关注最新社区动态。

## Cursor 白嫖

虽然靠无限 free trial 仍然适用，但是其质量已经不足以 validate 所付出的努力，不在讨论。
付费或者靠 cursor edu 即可。

## Augment 白嫖
vscode 外的同系产品，都需要手动下载/更新 vsix 插件。

### Free Trial
难点在于，注册账户。非常挑 IP 和浏览器环境。目前我的库里只有巴西和日本的 IP + 指纹浏览器随机指纹，可以注册成功。

一旦注册成功，登录环境就没关系了，当然最好第一次登录还是使用能通过注册的区域。之后我目前(2025.8月）是看来无所谓。

### 社区版
不挑 IP，首月 600 次，之后每月滚动给 50 次。截止 2025.8.2 已经失效

### 环境清理工具
- [Augment Cleaner](https://github.com/yuaotian/go-augment-cleaner) 不要用，乱七八糟一塌糊涂
- [Augment Free](https://github.com/BasicProtein/AugmentCode-Free) 本意是利用 augment 不断变化更新的规则，相对轻松的白嫖次数。截止 8.7 日，除了 7 日免费试用 125 次已经全都失效。本工具还有个重要功能是相对可靠的清理环境的 options, 以及对 augment 合理使用方式的清晰描述。比如他会很清楚的描述注册需要关注啥。
- [Augment Token Manager](https://github.com/zhaochengcube/augment-token-mng)  获取 access token ，也可以重置机器码，据说非常可靠。GUI 环境。
