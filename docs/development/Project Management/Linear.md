

github import 的对应关系是 organization -> team. 可以指定 org 中的特定 repos.

所以对我来说，可以多个 Linear account -> 单一 team -> 统一 github organization `mapledevs` 的不同 repo

GitHub repo 统一管理，免得维护太多 github credentials; 必要的时候升级 github pro, 值得；
GitHub 账户任何情况都没必要移交。

万一需要外包，可以单独共享某个 repo 

Linear 放在不同的账户管理， stay below free quota(250 issues is enough for the lifecyle of an SUV project)，暂时不用付费，而且便于统一移交所有权。

Linear 的不同账户都用各自 <EMAIL> 管理


![image.png](https://pimg.316444.xyz/2025/03/549cffe11e1bba712fea4de960ca51ab.png)
