---
sidebar_position: 1
title: VSCode
sidebar_label: VSCode
---



## Installations

## Local

务必从[可信来源](https://code.visualstudio.com/download)下载本地客户端

Extension Marketplace 本来就 host 在 CDN(应该是 Akamai) 就不用镜像了

桌面端安装后配置下 `code` Cli 工具，[操作说明](https://code.visualstudio.com/docs/editor/command-line)




### Remote

[Install Manually](https://code.visualstudio.com/docs/setup/linux) 

配置 repo 将来更新方便，因为更新还挺频繁，需要跟本地 vscode 版本对应；如果实在没有网络环境，就下载 deb 手动安装

```bash
# configure repo
sudo apt-get install wget gpg
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -D -o root -g root -m 644 packages.microsoft.gpg /etc/apt/keyrings/packages.microsoft.gpg
echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/keyrings/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" |sudo tee /etc/apt/sources.list.d/vscode.list > /dev/null
rm -f packages.microsoft.gpg

# install 
sudo apt install apt-transport-https
sudo apt update
sudo apt install code # or code-insiders

```





Ansible

```yaml
---
- name: Install VSCode CLI on Debian
  hosts: your_debian_hosts
  become: yes  # This allows Ansible to use sudo

  tasks:
    - name: Update apt cache
      apt:
        update_cache: yes

    - name: Install dependencies
      apt:
        name:
          - apt-transport-https
          - gnupg2
          - software-properties-common
        state: present

    - name: Add Microsoft GPG key
      apt_key:
        url: https://packages.microsoft.com/keys/microsoft.asc
        state: present

    - name: Add VSCode repository
      apt_repository:
        repo: deb [arch=amd64] https://packages.microsoft.com/repos/vscode stable main
        state: present
        filename: vscode

    - name: Install VSCode
      apt:
        name: code
        state: present
        update_cache: yes
```





你也可以直接访问 [vscode.dev](https://vscode.dev/) 



## 基本概念

**VS Code**（Visual Studio Code）是一个轻量级的代码编辑器，而不是一个传统的 IDE。它的核心功能是文本编辑，支持多种编程语言的语法高亮和基础代码补全。

- **轻量级编辑器**
  - **VS Code**（Visual Studio Code）是一个轻量级的代码编辑器，而不是一个传统的 IDE。它的核心功能是文本编辑，支持多种编程语言的语法高亮和基础代码补全。
  - 通过**扩展（Extensions）**，你可以将其功能扩展成接近 IDE 的体验。
- **扩展系统**
  - **Extensions** 是 VS Code 的核心功能之一。几乎所有高级功能都是通过扩展实现的，包括语言支持、调试、版本控制、代码格式化、主题等。
  - 可以通过快捷键 `Ctrl+Shift+X` 打开扩展市场，安装需要的扩展。
- **多语言支持**
  - VS Code 开箱即支持多种编程语言。通过安装扩展，你可以获得更强大的语言支持，如智能代码提示、Linting、调试等。
  - 常用语言的扩展有：Python、Java、C++、JavaScript 等。
- **集成终端**
  - VS Code 内置了终端，可以在编辑器中直接运行命令行操作，无需切换窗口。
  - 使用快捷键 `Ctrl+` (`Cmd+\`` on macOS) 打开终端，支持多终端会话。
- **工作区（Workspace）**
  - **工作区**（Workspace）是 VS Code 中用于组织项目的方式。你可以打开单个文件夹作为工作区，或者创建多根目录的工作区配置文件（`.code-workspace`）。
  - 工作区可以包含自定义的设置、调试配置、任务配置等。
- **调试**
  - VS Code 支持调试功能，但需要为每种语言安装相应的调试扩展（如 Python Debugger）。
  - 调试配置存储在 `.vscode/launch.json` 文件中，支持断点、变量监视、控制台输出等调试功能。
- **集成 Git**
  - VS Code 提供了开箱即用的 Git 集成功能，可以直接在编辑器中管理代码仓库。
  - 可以通过侧边栏的源代码管理图标或 `Ctrl+Shift+G` 打开 Git 界面，进行提交、推送、拉取等操作。
- **快捷键与命令面板**
  - VS Code 提供了丰富的快捷键（可以在 `Ctrl+K Ctrl+S` 查看和修改）。
  - **命令面板** (`Ctrl+Shift+P`) 是一个强大的工具，允许你通过输入命令名称来执行各种操作，类似于 IDEA 的 "Search Everywhere"。
- **设置与配置**
  - VS Code 提供了两种配置方式：图形界面和 `settings.json` 文件。用户可以根据个人喜好和项目需求定制编辑器。
  - 可以通过 `Ctrl+,` 打开设置界面，或者在工作区根目录下创建 `.vscode/settings.json` 文件为特定项目配置设置。

- **Task（任务）**
  - **Task** 是 VS Code 中的自动化工具，用于执行常见的命令行任务，比如编译代码、运行测试、打包项目等。
  - 你可以在 `.vscode/tasks.json` 文件中定义任务，然后通过命令面板或快捷键快速执行这些任务。

  - **主要特点**：
    - **自定义任务**：你可以定义各种类型的任务，指定命令、参数、工作目录、终端类型等。
    - **集成开发流程**：任务可以与其他 VS Code 功能结合使用，比如在调试前自动编译代码，或者在保存文件时自动运行测试。
    - **任务组**：你可以将多个任务组合在一起，按顺序执行，简化复杂的开发流程。
    - **运行模式**：支持后台任务、终端任务、Shell 脚本等多种运行模式。

- **Profile（配置文件）**
  - **Profile** 是 VS Code 中用来保存和管理用户设置、扩展、键绑定等个性化配置的工具。
  - 通过创建不同的 Profile，你可以在不同的工作环境（如不同项目、团队或语言环境）之间快速切换配置。

  - **主要特点**：
    - **定制化配置**：每个 Profile 可以包含不同的用户设置、扩展、调试配置等。适用于不同的项目或工作流需求。
    - **快速切换**：你可以通过命令面板或 UI 界面快速切换到不同的 Profile，切换过程中 VS Code 会自动加载相应的配置。
    - **导入/导出**：你可以将 Profile 导出，分享给团队成员或在其他设备上导入相同的开发环境。

## 配置

- Profile 一般无需配置，除非需要完全不同的 extension set, 相差迥异的 settings.json 配置
- 善用 Workspace 定义，维持在相对聚焦的一个工作台，不要容纳太多项目和文件
- Layout 很重要

### Settings

Levels of Settings:

1. User Settings: Apply globally to all VS Code instances
2. Workspace Settings: Apply to a specific workspace.
3. Folder Settings: Apply to a specific folder in a multi-root workspace.

### Working with remote servers

- `Remote-SSH` 不需要在服务端安装任何插件，会自动下载启动一个轻量级的 code server 以协调同步。
- Tunnel 需要安装code cli 并且运行一次 `code tunnel`建立连接后，每次 connect 还得保持其启动运行状态才行，需要自己写个 systemd service. 主要用于 firewall 后的服务器
- Sshfs 不要用，不成熟，且容易失误

如果开发 docker 部署的项目，而且需要频繁在远程服务器调试，可以使用 devcontainer

### Extensions

- Markdown Editor

  试了好几个，什么 All in One, markdown XX Editor, 都不能做到真正 WYSWYG 而且用习惯的快捷键。只有这个 [vscode-office](https://github.com/cweijan/vscode-office) 做到了相对均衡。其卖点是 PDF/WORD/XLS 文档预览，但 markdown 由于集成了 VDITOR 的代码，就很舒服。



### Layout

在 VS Code 中，**布局（layout）风格**和窗口的组织方式直接影响你的开发效率和体验。不同的开发任务和语言通常需要不同的布局风格，以下是一些常见的布局配置和使用方法，特别针对使用 Git、Python、Terminal、TypeScript 等的开发者。

#### 1. **基础布局组件**

- **侧边栏（Side Bar）**
  - **Explorer**：用于浏览和管理文件，通常放在左侧。
  - **Source Control（源代码管理）**：显示 Git 仓库的状态，进行提交、推送、分支管理等操作。
  - **Extensions（扩展）**：管理 VS Code 扩展。
  - **Search（搜索）**：全局搜索和替换项目中的文件内容。

- **面板（Panel）**
  - **Terminal（终端）**：通常放在窗口底部，支持多个终端会话。可以运行命令、编译代码、执行测试等。
  - **Problems（问题）**：显示编译或运行时的错误和警告。
  - **Output（输出）**：显示各种操作的日志和输出信息，如编译输出、Git 操作结果等。
  - **Debug Console（调试控制台）**：调试时使用，显示调试输出和表达式计算结果。

- **编辑器区域（Editor Area）**
  - 主要用于显示和编辑代码文件，支持多标签、多分屏（split view）操作。

#### 2. **常见的布局风格和使用方法**

##### A. **Git 集成开发**

- **布局建议**：
  - **左侧栏**：使用 **Source Control** 面板监控 Git 状态。
  - **面板区域**：保持 **Terminal** 和 **Output** 面板可见，以便快速查看 Git 命令输出和操作日志。
  - **编辑器区域**：主要用于查看和解决代码冲突，或进行代码审查（code review）。

- **使用方法**：
  - **分支管理**：在 Source Control 面板中切换、创建或合并分支。
  - **提交与推送**：在 Source Control 面板中选择要提交的更改并执行提交操作，随后推送到远程仓库。
  - **查看差异**：点击文件名查看文件的修改前后对比（diff）。

##### B. **Python 开发**

- **布局建议**：
  - **左侧栏**：使用 **Explorer** 浏览 Python 文件，结合 **Outline** 面板快速导航代码结构。
  - **面板区域**：保持 **Terminal** 打开，用于运行 Python 脚本、启动虚拟环境、运行测试等。
  - **编辑器区域**：编写 Python 代码，使用调试工具和 Linting 工具提升代码质量。

- **使用方法**：
  - **代码调试**：使用 **Debug** 面板启动 Python 调试会话，设定断点、监控变量和表达式。
  - **虚拟环境**：在 Terminal 中激活虚拟环境，并使用 VS Code 的 Python 扩展自动检测和使用它。
  - **代码格式化和 Linting**：通过安装 `Pylint`、`Black` 等扩展，确保代码风格统一和规范。

##### C. **终端密集型开发**

- **布局建议**：
  - **侧边栏**：可以关闭或最小化 Explorer，最大化编辑区域。
  - **面板区域**：将 **Terminal** 设置为主要面板，并使用多个终端分屏，处理不同的命令行任务（如构建、运行测试、服务器启动等）。

- **使用方法**：
  - **分屏终端**：在 Terminal 面板中创建多个终端，使用 `Split Terminal` 功能并行执行任务。
  - **快捷命令执行**：将常用的终端命令配置为 Task，快速执行而无需手动输入。
  - **任务链**：创建任务链，在开发过程中自动化多个终端任务的执行顺序。

##### D. **TypeScript 开发**

- **布局建议**：
  - **左侧栏**：使用 **Explorer** 和 **Outline** 面板快速导航 TypeScript 项目结构和文件。
  - **面板区域**：保持 **Problems** 面板和 **Terminal** 可见，随时监控 TypeScript 编译错误和运行脚本。
  - **编辑器区域**：多标签或分屏视图，一边编写代码，一边参考其他模块或接口定义。

- **使用方法**：
  - **自动编译**：配置 `tsc` 自动编译任务，保存时自动编译 TypeScript 文件。
  - **类型检查**：通过 Problems 面板快速定位类型错误，使用鼠标悬停查看类型提示。
  - **模块间导航**：使用快捷键（`Ctrl+Click`）快速跳转到类型定义或实现文件。

#### 3. **个性化布局设置与优化**

- **保存布局**：
  - VS Code 会自动保存你上一次关闭时的布局设置，所以不同项目可以根据需要调整布局，而不会互相干扰。

- **调整布局**：
  - 通过拖拽方式调整面板和编辑器的位置。也可以通过菜单 `View` -> `Appearance` -> `Move Panel Left/Right` 来调整面板位置。

- **扩展布局功能**：
  - 使用一些扩展如 **"Layout Manager"** 来保存和切换不同的布局配置，方便在不同开发场

### Workspace 

#### 创建和使用 Workspace:

1. **新建 Workspace 文件:**
   - 点击菜单 "文件" > "新建窗口" (macOS: "文件" > "新建")
   - 点击 "文件" > "将工作区另存为..." 并选择保存位置, 文件名通常以 `.code-workspace` 结尾.
   - 当然也可以直接编辑 `code-workspace` 文件
2. **添加项目文件夹:**
   - 在 VS Code 的资源管理器视图中，点击 “打开文件夹” 按钮，选择要添加的项目文件夹。
   - 你也可以直接将文件夹拖拽到 VS Code 窗口中。
3. **配置 Workspace:**
   - Workspace 配置存储在 `.code-workspace` 文件中，它是一个 JSON 格式的文件。
   - 常用的配置选项包括：
     - `"folders"`: 定义 Workspace 中包含的文件夹路径。
     - `"settings"`: 配置适用于该 Workspace 的 VS Code 设置。
     - `"extensions"`: 推荐适用于该 Workspace 的 VS Code 扩展。
     - `"tasks"`: 定义在该 Workspace 中可执行的任务。
     - `"launch"`: 定义调试配置。

#### 常见问题

##### 管理多个 remote folder 

```json
    "folders": [
        {
            "name": "HH Docker Config",
            "uri": "vscode-remote://tunnel+hh/opt/docker"
        },
        {
            "name": "NL Home",
            "uri": "vscode-remote://ssh-remote+nl/home/<USER>"
        }
    ]
```

确实可以打开，但没有意义，因为 server 并不会自动 connect, remote folder 还得手动 open folder，也并不能使用 remoteAuthority 

所以建议

```json
{
		"folders": [
        {
            "name": "HH Docker",
            "uri": "vscode-remote://ssh-remote+hh/opt/docker"
        }
    ],
    "remoteAuthority": "ssh-remote+hh"
}
```

然后在这个 workspace 的 remote explorer 手动操作 connect 

##### workspace 的独立 settings

for multiroot workspace

- Workspace File: `/path/to/your/project.code-workspace`
- Workspace Settings: `<workspace_folder>/.vscode/settings.json`
  (Where `<workspace_folder>` is the folder containing the `.code-workspace` file)



#### 最佳实践

- **合理组织:** 将 Workspace 文件存储在专门的目录中，方便管理和查找。如 `~/vscode-workspaces`
- **版本控制:** 将 Workspace 文件纳入版本控制系统，方便团队协作和跟踪变更。

- **充分利用设置:** 为每个 Workspace 配置合适的编辑器设置、扩展推荐、任务和调试配置。

- Workspace 的内容很灵活，可以是 local folder, remote folder, 和另外一个 workspace 

```json
{
  "folders": [
    { "name": "Workspace Launcher", "path": "." },
    { "name": "Frontend Projects", "path": "./frontend" },
    { "name": "Backend Projects", "path": "./backend" },
    { "name": "Production Servers", "path": "./servers/production.code-workspace" },
    { "name": "Development Servers", "path": "./servers/development.code-workspace" }
  ],
  "settings": {
    "files.exclude": {
      "**/*.code-workspace": true
    }
  }
}
```

- **one Master Workspace to rule them all** 

  - Create a Master Workspace File:
    Create a new .code-workspace file, let's call it master-workspace.code-workspace.

  - Structure the Master Workspace:
    In this file, you'll list all your other workspace files as folders. Here's an example structure:

```json
{
  "folders": [
    {
      "name": "Workspace Launcher",
      "path": "."
    },
    {
      "name": "Project A",
      "path": "./project-a.code-workspace"
    },
    {
      "name": "Project B",
      "path": "./project-b.code-workspace"
    },
    {
      "name": "Servers",
      "path": "./servers.code-workspace"
    }
  ],
  "settings": {
    "files.exclude": {
      "**/*.code-workspace": true
    }
  }
}
```



#### 应用场景示例:

- **全栈开发:** 将前端项目和后端 API 项目添加到同一个 Workspace，方便同时调试和开发。
- **文档编写:** 创建一个专门用于文档编写的 Workspace，配置 Markdown 预览、拼写检查等功能。
- **远程开发:** 使用 "Remote - SSH" 扩展连接到远程服务器，并将远程文件夹添加到 Workspace，实现无缝的远程开发体验。



### Tasks

#### Open in ..

最蛋疼的就是 markdown 编辑了

```
{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
      {
        "label": "Open in Typora",
        "type": "shell",
        "command": "open", 
        "args": [
          "-a", 
          "Typora", 
          "${file}" 
        ],
        "presentation": {
          "reveal": "always"
        },
        "problemMatcher": [],
        "group": {
          "kind": "build",
          "isDefault": true
        }
      }
    ]
}
```





## Develop in Action

