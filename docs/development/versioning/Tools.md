- **GitKraken**. 
	- 订阅付费模式，包含在 [GitHub 学生包]()中。可以登陆后设置密码然后单独登录，就无需 GitHub social login 了。Pro 的有效期不明。
	- vscode extension 是 GitLens
	- 之后只能 connect 绑定的 GitHub 账户(准确的说 connect 其它的也可以，但是有风险)
	- native support `GitLab`, `BitBucket`, `<PERSON>ra`, `Trello` 等
	总体上相对 Tower 更成熟完善，也提供免费的 free tier. 
- **Tower**. 
	- 有破解版，也包含在 GitHub 学生包中，会得到一个 license key，有效期一年。到期后政策不明
- **Kaleidoscope**. git merge/diff tool. 很棒
	- 用破解版
	- 支持 CLI tool `ksdiff` ，可以配置在 vscode, git config 里。直接在界面里按说明操作即可。
- **lazygit**  得学习下
- gcm. git-credentials-manager

### GCM
[Git Credential Manager](https://github.com/git-ecosystem/git-credential-manager/tree/release) 跨平台安全管理 git 登录鉴权的服务，支持 MFA

- Installation on macOS
```
# Install
brew install --cask git-credential-manager

# 确保 gcm 是唯一的 credential helper
vim ~/.gitconfig

[credential]
        helper = /usr/local/share/gcm-core/git-credential-manager
        
# Remove the existing credentials:
git credential reject
protocol=https
host=git.lvtu.in

# Try to clone or interact with your repository:
git clone https://git.yourdomain.com/username/repo.git

# or if you're working with an existing repo:
git pull
```
会弹出 GCM 窗口输入鉴权信息，务必使用 PAT(Personal Access Token)而不是 password. gitea 创建 PAT 的时候注意赋予 repo&issues READ/WRITE 的权限。


- Installation on debian linux
Download the latest [.deb package](https://github.com/git-ecosystem/git-credential-manager/releases/latest), and run the following:

```
sudo dpkg -i <path-to-package>
git-credential-manager configure
```