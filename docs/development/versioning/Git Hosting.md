## Managed Hosting
Github、GitLab 还是王者，都提供非常 generous 的免费计划。

**Comparison their Free Plan**
> 来自 pplx, 不一定准确。

另外，GitHub 支持 Git LFS 存储，单个文件建议不要超过 1GB，但应该也都是 soft limit. 
GitHub 有流行的学生包教育计划，GitLab 也有但是没人玩儿，可能审查严格吧。

| Feature                      | **GitLab Free**               | **GitHub Free**                     |
| ---------------------------- | ----------------------------- | ----------------------------------- |
| **Private Repositories**     | Unlimited                     | Unlimited                           |
| **Storage (Private Repos)**  | 5 GB per repo                 | 500 MB per repo                     |
| **Collaborators**            | Unlimited                     | Unlimited                           |
| **CI/CD Minutes**            | 400/month                     | 2,000/month (private repos only)    |
| **Transfer Bandwidth**       | 10 GB/month                   | Not explicitly limited              |
| **Issue Tracking**           | Robust integrated tools       | Basic issue tracking                |
| **Large File Storage (LFS)** | Not included in the free plan | 1 GB storage + 1 GB bandwidth/month |
### Github


### GitLab
free tier 不支持用 `pull` 方式从 github 等外部 repo 拉数据。


### gitgud
[signup](https://gitgud.io/users/sign_in)
使用 GitLab, free hosting, 很慷慨的 quota


## Selfhosted 
### Gitea

### Gitlab
占用资源奇高，没有 16G mem 和强力 CPU 就别跑了，这还没算 CI/CD 和 backup

目前完整文档还在[飞书](https://paratrips.feishu.cn/wiki/GzwFwMiADiSyzikBkQYcsSAQnee)

## Best Practice
本地 git dev 使用