---
sidebar_position: 1
title: Awesome Serverless
description: 我们需要关心的 serverless 解决方案和资源
---


# :cloud: Awesome Serverless

A curated list of awesome services, solutions and resources for serverless / nobackend applications.

***“No server is easier to manage than no server”***

## TODO

- [ ] 添加标签，类似 database 里的样式
- [ ] 加点学习资料
- [ ] 汇总大厂 Platform

## What is "serverless" computing?

> The phrase “serverless” doesn’t mean servers are no longer involved. It simply means that developers no longer have to think "that much" about them. Computing resources get used as services without having to manage around physical capacities or limits.
> Let's take for example AWS Lambda. "Lambda allows you to NOT think about servers. Which means you no longer have to deal with over/under capacity, deployments, scaling and fault tolerance, OS or language updates, metrics, and logging."

[Quora: What is Serverless Computing?](https://www.quora.com/What-is-serverless-computing)


## Databases

### AWS

* [Amazon DynamoDB](https://aws.amazon.com/dynamodb/) - Flexible NoSQL database service
* [Amazon Aurora Serverless](https://aws.amazon.com/rds/aurora/serverless/) - Serverless MySQL Database service

### General Use

- [Supabase](https://supabase.com/) - pg based, extremely easy to setup for full-fledged serverless applications. Opensource 项目提供集群部署，edge functions.  #freetier #nocc #opensource
- [Aiven](https://aiven.io/) - pg/mysql hosting platform. #freetier #nocc
- [CockroachDB](https://www.cockroachlabs.com/) - pg based, 号称打不死的小强级别的 PG. 计费规则比较复杂 #freetier #nocc #opensource
- [Turso](https://turso.tech/) - hosted sqlite based database. 亮点是 [multi-schema](https://docs.turso.tech/concepts), 部署和 replicas 的节点很多。libsql 还在 experimental 阶段  #freetier #nocc #opensource
* [FaunaDB](https://fauna.com/) - Pay-as-you-go cloud database with ACID transactions and on-premise licensing available. #freetier #nocc
* [Cloudflare Workers KV](https://developers.cloudflare.com/workers/reference/storage) - Global, low-latency, key-value data store for Cloudflare Workers.  #freetier #nocc
* [Upstash](https://upstash.com/) - Serverless Database for Redis/Kafka/Pub/Sub Queue.  #freetier #nocc
* [Neon](https://neon.tech) - fully managed serverless PostgreSQL. Neon separates storage and compute to offer modern developer features such as serverless, branching, bottomless storage, and more. #freetier #nocc

## Platforms

* [Tau](https://github.com/taubyte/tau) - Easily Build Cloud Computing Platforms with features like Serverless WebAssembly Functions, Frontend Hosting, Object Storage, K/V Database, and Pub-Sub Messaging.
* [AWS Lambda](https://aws.amazon.com/lambda) - Run code without thinking about servers. Pay for only the compute time you consume.
* [Google Cloud Functions](https://cloud.google.com/functions/docs) - Lightweight, event-based, asynchronous compute solution that allows you to create small, single-purpose functions that respond to cloud events without the need to manage a server or a runtime environment.
* [Azure Functions](https://azure.microsoft.com/en-us/services/functions) - Listen and react to events across your stack.
* [Fission](http://fission.io) - Fast, extensible, open source serverless functions on any Kubernetes cluster
* [IBM Cloud Functions](https://console.bluemix.net/openwhisk/) - Distributed compute service to execute application logic in response to events.
* [Knative](https://knative.dev/) - Kubernetes-based platform to build, deploy, and manage modern serverless workloads
* [Surge](http://surge.sh) - Deploy static sites from the command line.
* [Netlify](https://netlify.com) - All-in-one platform for automating modern web projects. Build and host static sites, deploy AWS lambda functions, and more, all from git repositories.
* [Vercel](https://vercel.com) - Build and deploy 40 different web frameworks with zero configuration along with Node.js, Python, Go, and Ruby Serverless Functions
* [Back4App](https://www.back4app.com) - 一个 256MB 的 container instance 免费, 不过最简单的使用 container 部署居然不支持，非要找 Dockerfile 自己 build, `dockerfile` 都不行  #freetier #nocc
* [NorthFlank](https://northflank.com/pricing)  可以运行 container, 但是需要绑定信用卡，就一点点资源，感觉不值当。  #cc #freetier
* Railway
* Fly.io  虽然基础计划免费，但需要绑卡，而且资源付费  #cc  #freetier
* Zeapur  已经不能运行 container, 尝试跑了 uptime kuma，过了几天就被停机了
* [Cloudflare Workers](https://www.cloudflareworkers.com) - Workers allow you to deploy Serverless apps to 165+ data centers across the globe simultaneously, along with the ability to agument or alter exsisting websites and APIs on the fly.

## Frameworks

* [AWS Charlice](https://github.com/aws/chalice) -  framework for writing serverless apps in python
* AWS Serverless Application Model (AWS SAM)
* [Apache OpenWhisk](https://openwhisk.apache.org) - Open source and enterprise-ready serverless platform that executes functions in any language (including Docker Containers) in response to events, powering IBM Cloud Functions, Adobe I/O Runtime and a number of on-prem deployments worldwide.
* [Architect](http://arc.codes) - Provision and deploy from a super simple plaintext manifest.
* [ClaudiaJS](https://github.com/claudiajs/claudia) - Deploy Node.js microservices to AWS easily.
* Jets
* [Parse Server](https://parseplatform.org/) - Parse Server is an open source version of the Parse backend that can be deployed to any infrastructure that can run Node.js. You can find the source on the [GitHub repo](https://github.com/parse-community/parse-server).
* [Pulumi](https://pulumi.io) - A cloud development platform for serverless, containers, infrastructure. Multi-cloud (and Kubernetes) and works with JS, TS, Python, Go
* [serverless-cqrs](https://www.serverless-cqrs.com) - A collection tools to help you build a fully functioning backend based on the principles of CQRS, Event Sourcing, Domain Driven Design, and Onion Architecture.
* [Serverless Framework](http://www.serverless.com) - Build and maintain web, mobile and IoT applications running on AWS Lambda, Azure Cloud Functions, IBM Cloud Functions, Apache OpenWhisk, and Google Cloud Functions (formerly known as JAWS).
* [Stacktape](https://stacktape.com) - DevOps-free cloud framework. Deploy lambdas, containers, databases & more to AWS with 98% less config.
* [Zappa](https://github.com/zappa/Zappa) - Serverless Python WSGI with AWS Lambda + API Gateway.
* [ShipYard](https://shipyard.build/) -  The Ephemeral Environment Self-Service Platform
* [Convey](https://www.convex.dev/) FE backend framework

## GPU/LLM
- [AWS SageMaker](https://aws.amazon.com/sagemaker/) - Build, train, and deploy machine learning models at scale.
- [Deepnote](https://deepnote.com/) - Deep learning in the browser.
- [Hugging Face Spaces](https://huggingface.co/) - Build, train, and deploy NLP models.
- [BentoML](https://www.bentoml.com/) - BentoML is a platform for serving, managing, and deploying machine learning models at scale.
- [Modal](https://modal.com/) - Modal is a serverless GPU provider for AI and ML.

## Security

> 没看到更新的综述。很重要的话题，先留着

* [Awesome-Serverless-Security](https://github.com/puresec/awesome-serverless-security/) - A curated list of awesome serverless security resources such as (e)books, articles, whitepapers, blogs and research papers.

## CI/CD

之前的列表我都给删了，目前看来最好的 serverless 方案就是 GitHub Action Runner



## Observability Tools (Logging / Monitoring / Performance / Tracing)

> 之前的我没看就全给删了，最近看资料，基本上就是这两个产品主导

- Datadog
- Sentry

## Authentication and authorization

* [Auth0](https://auth0.com) - Single Sign On & Token Based Authentication.
* Clerk
* [Serverless Authentication Boilerplate with FaunaDB](https://serverless.com/blog/faunadb-serverless-authentication/) - Single sign on using Amazon API Gateway custom authorizer to provision database access tokens for your Lambda functions. 未经检查
* supabase+next.js 

## Payments

* Stripe
* FastSpring. 据说就是套壳 Stripe, 手续费高一些，后台更弱



## Content Management Systems

> 我全给删了，但留着 placeholder. 实在太多工具了，连 Notion 都可以用来创建一个 serverless content site

### Forms

> 现在 serverless framework 都已经快要成引流产品了，Form 这种初级应用已经真正可以 nocode 几分钟搭建。但很有意思的是仍然有很多活跃产品服务。

* [Airform.io](https://airform.io) - Functional HTML forms for Front-End Developers.
* [Form.io](https://form.io) - JSON Powered Form and Data Management Platform for Serverless applications.
* [Formcake](https://formcake.com) - Form backend built for developers with spam protection, Zapier integration, and API access.
* [Formcarry](https://formcarry.com) - Hassle-free HTML form endpoints for your form, powerful dashboard, reliable spam blocking, attachment uploads and Zapier integrations.
* [Formspark](https://formspark.io) - A backend for your HTML forms.
* [Formspree](https://formspree.io) - Functional HTML forms.
* [FormKeep](https://formkeep.com) - Form endpoints for designers and developers. No iframes, JavaScript embeds, or CSS overrides.
* [Formplug](https://github.com/danielireson/formplug-serverless) - Form forwarding service for AWS Lambda.
* [FormAssembly](http://www.formassembly.com/).
* [Getform](http://getform.io/) - Free form backend platform for your HTML forms and static websites. Provides Zapier and Webhook support and submissions API.
* [Google Forms](https://docs.google.com/forms/) - Create and analyze online forms and surveys.
* [Pageclip](https://pageclip.co/) - A Server for your HTML Forms - Collect info from users without a server—Pageclip is your server. Lead capture forms, surveys, newsletter forms, contact forms, etc. Setup any form in seconds.
* [Typeform](https://www.typeform.com/) - Pretty, intuitive, slick forms for almost any use.
* [serverless forms](https://serverlessforms.com)

## Realtime

* [Ably](https://www.ably.io/) - Global distributed realtime data delivery platform with pub/sub, presence, device awareness, history, connection state recovery, authentication and encryption.
* [Pusher](https://pusher.com/) - Build Apps, Not Infrastructure.
* [Pubnub](https://www.pubnub.com/) - PubNub utilizes a Publish/Subscribe[2] model for realtime data streaming.
* sendbird
* talk.js
* Stream 


## Message sending

* Mailchimp
* mailgun
* sendgrid
* twilio 
* [Amazon SNS](https://aws.amazon.com/sns) - A flexible, fully managed pub/sub messaging and mobile notifications service (including SMS) for coordinating the delivery of messages to subscribing endpoints and clients.

## References

- 本文源自 [anaibol/awesome-serverless](https://github.com/anaibol/awesome-serverless/tree/master), 但非常啰嗦，不是 developer's view
- [Directory](https://www.serverless.com/examples) - 按 platform/language 检索 serverless code examples
- 

### Related articles

* [Serverless Framework (CloudAcademy)](http://cloudacademy.com/blog/serverless-framework-aws-lambda-api-gateway-python) -  A Deep Overview of the Best AWS Lambda + API Gateway Automation Solution
* [AWS Lambda Microservices Architecture for Node.js](https://medium.com/getty-logs/a-aws-lambda-microservices-architecture-for-node-js-4513799101d4#.k99m6yvvz)
* [Designing Teams around Microservices](https://www.nginx.com/blog/adopting-microservices-at-netflix-lessons-for-team-and-process-design/)
* [The Serverless Start-Up - Down With Servers!](http://highscalability.com/blog/2015/12/7/the-serverless-start-up-down-with-servers.html)
* [Think Serverless!](https://medium.com/think-serverless) - Publications encompassing a deep insight into the future of serverless application development
* [Microservices without the Servers](https://aws.amazon.com/blogs/compute/microservices-without-the-servers)
* [A startup journey on AWS: from bare metal monolith to serverless microservices](https://medium.com/@benorama/a-startup-journey-on-aws-from-bare-metal-monolith-to-serverless-microservices-80231624fbd9)
* [nobackend.org](http://nobackend.org)
* [unhosted.org](https://unhosted.org/) - unhosted web apps. freedom from web 2.0's monopoly platforms
* [Static Web Applications](https://staticapps.org)
* [Serverlesscode](https://serverlesscode.com)
* [Serverless Architectures](http://martinfowler.com/articles/serverless.html)
* [Serverless Stack](http://serverless-stack.com/) - A step-by-step guide to creating full-stack serverless apps.
* [Migrating a Native JAVA REST API to a Serverless Architecture with the Lambada Framework for AWS](https://aws.amazon.com/blogs/compute/migrating-a-native-java-rest-api-to-a-serverless-architecture-with-the-lambada-framework-for-aws/)
* [Using Kotlin with AWS Lambda](https://medium.com/tech-travelstart/using-kotlin-in-a-serverless-architecture-with-aws-lambda-part-1-setting-up-the-project-87033790e2f4) - Using Kotlin in a serverless architecture with AWS Lambda.
* [Serverless Architectures Security Top 10](https://github.com/puresec/sas-top-10) - The Ten Most Critical Security Risks in Serverless Architectures.
* [Continuous Delivery Patterns with Serverless Applications](https://semaphoreci.com/blog/2018/08/22/continuous-delivery-patterns-with-serverless.html) - patterns for effective Continuous Delivery when building Serverless applications.
* [Level up your serverless game with a GraphQL data-as-a-service layer](https://hasura.io/blog/level-up-your-serverless-game-with-a-graphql-data-as-a-service-layer/)
* [Serverless White Paper](https://www.unusual.vc/post/unusual-tech-serverless-white-paper) - Informative White Paper on Serverless with deep analysis by Unusual VC.
* [Receiving Webhooks with Netlify Functions](https://www.svix.com/blog/receive-webhooks-with-netlify-functions/) - Using Netlify's serverless functions to receive webhooks and verify the signature.

### Courses

* [Serverless JavaScript by Example](https://www.packtpub.com/web-development/serverless-javascript-example-video) - Become dexterous with live demonstrations on serverless web development.
* [AWS Lambda in Motion](https://www.manning.com/livevideo/aws-lambda-in-motion) - An example-driven tutorial that focuses on helping you understand and tackle the operational challenges with running AWS Lambda in production.
* [Serverless Applications with AWS](https://www.manning.com/livevideo/serverless-applications-with-AWS) - Serverless Applications with AWS teaches you to build serverless applications using AWS Lambda and other cloud-based services.
* [Serverless-stack](https://serverless-stack.com) - Learn to Build complete Full-Stack Apps with Serverless and React on AWS.
* [Develop a Serverless Backend using Node.js on AWS Lambda](https://egghead.io/courses/develop-a-serverless-backend-using-node-js-on-aws-lambda) - Learn how to create a serverless API and connecting it to DynamoDB using leveraging Lambda's new async/await syntax
* [AWS in Motion](https://www.manning.com/livevideo/aws-in-motion) - A liveVideo course that guides you through your first steps of deploying a web application on AWS, teaching you the basics of the AWS ecosystem.
* [Production-Ready Serverless](https://www.manning.com/livevideo/production-ready-serverless) - This liveVideo teaches you how to build applications that take advantage of AWS Lambda and other AWS platform features like API Gateway and Kinesis.



