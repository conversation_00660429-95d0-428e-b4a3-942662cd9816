# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
dist/

# IDEs and Editors
.vscode/
.idea/
*.swp
*.swo
*~

# pnpm
pnpm-debug.log
.pnpm-store/
pnpm-lock.yaml

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
# Jupyter Notebook
.ipynb_checkpoints
*.log
.pytest_cache/
.aider*

# Node modules
node_modules/

# Build output directories
build/
docs/build/ 

# macOS
.DS_Store

# Editor and IDE files
.idea/
.vscode/
*.iml
*.swp
*.swo
*.log

# Operating system files
Thumbs.db

# Docusaurus cache 
.docusaurus

# Local configuration (if you have any)
docusaurus.config.local.js

# Package lock files (let your package manager handle these)
package-lock.json
yarn.lock

# Obsidian
.obsidian/
.trash/
.DS_Store
.MWebMetaData