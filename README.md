# NeXus Documentation

Welcome to the NeXus documentation repository! This README will guide you through the structure of our documentation site and how to contribute to it.

## Overview

This documentation site is built using [Docusaurus 3](https://docusaurus.io/), a modern static website generator. It allows us to write our documentation in Markdown and automatically generates a user-friendly, searchable website.

Our documentation covers various aspects of NeXus, including business strategies, service architecture, development practices, and DevOps processes.

## Repository Structure

- `/docs`: Contains all the documentation files in Markdown format.
- `/src`: React components and pages for the website.
- `/static`: Static assets like images.
- `/blog`: Blog posts in Markdown format.
- `docusaurus.config.js`: Main configuration file for the Docusaurus site.
- `sidebars.js`: Configures the sidebar navigation.

## Development Options

You have two options for development:

### Option 1: Local Development with pnpm

1. Clone the repository:
   ```bash
   git clone https://git.lvtu.in/nexus/nexus-docs.git
   ```

2. Install pnpm if you haven't already:
   ```bash
   npm install -g pnpm
   ```

3. Install dependencies:
   ```bash
   pnpm install
   ```

4. Run the development server:
   ```bash
   pnpm start
   ```

### Option 2: Docker Development (Recommended)

This option doesn't require any local Node.js setup:

1. Clone the repository:
   ```bash
   git clone https://git.lvtu.in/nexus/nexus-docs.git
   ```

2. Start the Docker container:
   ```bash
   docker compose up --build
   ```

The site will be available at `http://localhost:3001`. The build output will be available in the `./build` directory.

## Deployment to Cloudflare Pages

1. Install Wrangler CLI if you haven't already:
   ```bash
   npm install -g wrangler
   ```

2. Login to Cloudflare:
   ```bash
   wrangler login
   ```

3. Deploy to Cloudflare Pages:
   ```bash
   wrangler pages deploy build
   ```

## Adding New Documentation

1. Create a new Markdown file in the appropriate subdirectory under `/docs`.
2. Add front matter at the top of your Markdown file:
   ```markdown
   ---
   sidebar_position: 1
   title: Your Document Title
   sidebar_label: Sidebar Label (optional)
   description: A brief description of the document content (optional)
   keywords: [keyword1, keyword2, keyword3]
   tags: [tag1, tag2]
   ---
   ```
3. Write your documentation in Markdown format.

## Editing Existing Documentation

1. Find the relevant Markdown file in the `/docs` directory.
2. Make your changes, ensuring to maintain the existing structure and front matter.
3. Preview your changes using either the local development or Docker setup.

## Adding Blog Posts

1. Create a new Markdown file in the `/blog` directory with the format `YYYY-MM-DD-title.md`.
2. Add front matter at the top of your blog post:
   ```markdown
   ---
   slug: post-url
   title: Your Blog Post Title
   authors: 
     - name: NeXus Team
       title: NeXus Content Team
       url: https://nexus.com/team
       image_url: https://github.com/author.png
   tags: [tag1, tag2]
   description: A brief description of your blog post (optional)
   ---
   ```
3. Write your blog post content in Markdown.

## Best Practices

- Use clear, concise language.
- Include code examples where appropriate.
- Use headings to organize your content.
- Add images to illustrate complex concepts (store them in `/static/img`).
- Link to other relevant documentation pages.
- Keep the content aligned with NeXus's focus on AI, rapid development, and global market strategies.

## Note on Build Output

When using Docker, the build output will automatically be available in the `./build` directory. If you're using local development with pnpm, you can build the site using:

```bash
pnpm build
```
