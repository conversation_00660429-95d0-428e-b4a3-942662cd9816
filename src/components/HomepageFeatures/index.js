import React from 'react';
import clsx from 'clsx';
import styles from './styles.module.css';

const FeatureList = [
  {
    title: 'AI-Driven Innovation',
    description: (
      <>
        Leverage cutting-edge AI technologies to drive business growth and technological advancement.
      </>
    ),
  },
  {
    title: 'Rapid Development & Delivery',
    description: (
      <>
        Implement agile methodologies and DevOps practices to accelerate time-to-market for innovative solutions.
      </>
    ),
  },
  {
    title: 'Global Market Strategies',
    description: (
      <>
        Develop and execute strategies to succeed in diverse regional markets worldwide.
      </>
    ),
  },
];

function Feature({title, description}) {
  return (
    <div className={clsx('col col--4')}>
      <div className="text--center padding-horiz--md">
        <h3>{title}</h3>
        <p>{description}</p>
      </div>
    </div>
  );
}

export default function HomepageFeatures() {
  return (
    <section className={styles.features}>
      <div className="container">
        <div className="row">
          {FeatureList.map((props, idx) => (
            <Feature key={idx} {...props} />
          ))}
        </div>
      </div>
    </section>
  );
}
