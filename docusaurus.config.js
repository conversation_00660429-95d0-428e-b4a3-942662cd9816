/** @type {import('@docusaurus/types').Config} */
const config = {
  title: 'NeXus Docs',
  tagline: 'Pioneering IT solutions in the AI era',
  url: 'https://docs.nexus.com',
  baseUrl: '/',
  favicon: 'img/nexus-favicon.ico',
  organizationName: 'nexus',
  projectName: 'nexus-docs',

  i18n: {
    defaultLocale: 'en',
    locales: ['en'],
  },

  presets: [
    [
      '@docusaurus/preset-classic',
      /** @type {import('@docusaurus/preset-classic').Options} */
      {
        theme: {
          customCss: require.resolve('./src/css/custom.css'),
        },
        docs: {
          sidebarPath: require.resolve('./sidebars.js'),
          routeBasePath: '/',
          editUrl: 'https://git.lvtu.in',
        },
        sitemap: {
          changefreq: 'weekly',
        },
        blog: false,
      },
    ],
  ],

  plugins: [
    [
      '@docusaurus/plugin-content-blog',
      {
        id: 'blog',
        routeBasePath: 'blog',
        path: './blog',
        showReadingTime: true,
        editUrl: 'https://git.lvtu.in',
        authorsMapPath: 'i18n/en/docusaurus-plugin-content-blog/authors.yml',
      },
    ],
  ],

  themes: ['@docusaurus/theme-mermaid'],
  markdown: {
    mermaid: true,
  },

  themeConfig: {
    navbar: {
      title: 'NeXus Docs',
      logo: {
        alt: 'NeXus Logo',
        src: 'img/nexus-logo.svg',
      },
      items: [
        {
          type: 'docSidebar',
          sidebarId: 'tutorialSidebar',
          position: 'left',
          label: 'Documentation',
        },
        {to: '/blog', label: 'Blog', position: 'left'},
        {
          href: 'https://git.lvtu.in',
          label: 'codebase',
          position: 'right',
        },
      ],
    },
    footer: {
      style: 'dark',
      links: [
        {
          title: 'Community',
          items: [
            {
              label: 'Stack Overflow',
              href: 'https://stackoverflow.com/questions/tagged/nexus',
            },
            {
              label: 'Discord',
              href: 'https://discordapp.com/invite/nexus',
            },
            {
              label: 'Twitter',
              href: 'https://twitter.com/nexus',
            },
          ],
        },
        {
          title: 'More',
          items: [
            {
              label: 'Blog',
              to: '/blog',
            },
            {
              label: 'codebase',
              href: 'https://git.lvtu.in',
            },
          ],
        },
      ],
      copyright: `Copyright © ${new Date().getFullYear()} NeXus, Inc.`,
    },
  },
  customFields: {
    outDir: 'custom-build-directory',
  },
};

module.exports = config;
